package com.qm.ep.rebatecalc.monitor.threadpool.report;

import com.alibaba.fastjson.JSON;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ThreadPoolDetailInfo {

    private Integer port;
    private String applicationName;
    private String ip;
    private String poolName;
    private Integer maximumPoolSize;
    private Integer corePoolSize;
    private Integer activePoolSize;
    private Long keepAliveTime;
    private Integer queueCapacity;
    private Integer queueSize;
    private boolean preStartAllCoreThreads;
    private boolean preStartCoreThread;
    private long completedTaskCount;
    private long errorTaskNum;
    private String rejectedExecutionType;
    private String taskCountScoreThreshold;
    private String recordTime;

    public String toJson(){
        return JSON.toJSONString(this);
    }
}
