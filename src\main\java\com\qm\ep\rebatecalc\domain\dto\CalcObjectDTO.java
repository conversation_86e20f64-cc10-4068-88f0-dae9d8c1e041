package com.qm.ep.rebatecalc.domain.dto;

import com.qm.ep.rebatecalc.enumerate.CalcObjectTypeEnum;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "CalcObjectDTO对象")
@Data
@ToString(callSuper = true)
public class CalcObjectDTO extends JsonParamDto {

    @Schema(description = "序列化Id")
    private static final long serialVersionUID = 1L;

    @Schema(description = "政策主键")
    private String policyId;

    @Schema(description = "所属公司")
    private String companyId;

    @Schema(description = "经销商代码")
    private String dealerCode;

    @Schema(description = "计算对象ID")
    private String objectId;

    @Schema(description = "计算对象类型")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "是否使用缩略别名")
    private Boolean abbreviated;
}
