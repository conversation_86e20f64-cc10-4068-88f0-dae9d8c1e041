package com.qm.ep.rebatecalc.config;

import com.qm.ep.rebatecalc.ds.decorator.ContextDecorator;
import com.qm.ep.rebatecalc.monitor.annotation.Monitor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Configuration
@EnableAsync
@Monitor
@RefreshScope
public class AsyncCommonConfig {

    @Value("${task.common.execution.pool.core-size:20}")
    private int corePoolSize;
    @Value("${task.common.execution.pool.max-size:30}")
    private int maxPoolSize;
    @Value("${task.common.execution.pool.queue-capacity:1000}")
    private int queueCapacity;
    @Value("${task.common.execution.thread-name-prefix:task-}")
    private String namePrefix;
    @Value("${task.common.execution.pool.keep-alive:10}")
    private int keepAliveSeconds;


    @Bean
    public Executor dataClearAsync() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //核心线程数
        executor.setCorePoolSize(corePoolSize);
        //任务队列的大小
        executor.setQueueCapacity(queueCapacity);
        //线程前缀名
        executor.setThreadNamePrefix(namePrefix + "dataClearAsync-");
        //线程存活时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 增加 TaskDecorator 属性的配置
        executor.setTaskDecorator(new ContextDecorator());

        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //线程初始化
        executor.initialize();
        return executor;
    }

}