package com.qm.ep.rebatecalc.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Schema(description = "复制Remote接收数据")
@Data
public class CopyRemoteReceiveDTO {
    @Schema(description = "计算对象 ID")
    private String calcObjectId;
    @Schema(description = "计算对象对象 ID ")
    private String calcObjectIdNew;
    @Schema(description = "策略 ID ")
    private String policyIdNew;
}