package com.qm.ep.rebatecalc.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AimDetailDTO implements Serializable {

    @Schema(description = "序列化Id")
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private String id;
    @Schema(description = "目标 ID")
    private String aimId;
    @Schema(description = "法典")
    private String code;
    @Schema(description = "类别")
    private String category;
    @Schema(description = "名字")
    private String name;
    @Schema(description = "价值")
    private String value;
    @Schema(description = "单位")
    private String unit;
}
