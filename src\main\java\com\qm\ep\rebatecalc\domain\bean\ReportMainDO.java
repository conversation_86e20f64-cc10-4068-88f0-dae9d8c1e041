package com.qm.ep.rebatecalc.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("REPORT_MAIN")
@Schema(description = "报表明细主表对象")
public class ReportMainDO implements Serializable {

    @Schema(description = "序列化id")
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "报表名称")
    @TableField("REPORT_NAME")
    private String reportName;

    @Schema(description = "兑付对象")
    @TableField("CASH_OBJECT")
    private String cashObject;

    @Schema(description = "报表描述")
    @TableField("DESCRIPTION")
    private String description;

    @Schema(description = "计算状态")
    @TableField("CALCULATION_STATUS")
    private Integer calculationStatus;

    @Schema(description = "开始计算时间")
    @TableField(value = "BEGIN_TIME")
    private Date beginTime;

    @Schema(description = "结束计算时间")
    @TableField(value = "END_TIME", updateStrategy = FieldStrategy.IGNORED)
    private Date endTime;

    @Schema(description = "创建者")
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建日期")
    @TableField(value = "CREATE_ON", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "更新者")
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "更新日期")
    @TableField(value = "UPDATE_ON", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;



}
