package com.qm.ep.rebatecalc.domain.dto;

import com.qm.ep.rebatecalc.enumerate.CalcTypeEnum;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "计算对象计算结果信息")
@Data
public class ExecCalcResultDTO extends JsonParamDto {

    @Schema(description = "序列化Id")
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private String id;

    @Schema(description = "历史记录 ID")
    private String historyId;

    @Schema(description = "计算类型")
    private CalcTypeEnum calcType;

    @Schema(description = "经销商代码")
    private String dealerCode;

}