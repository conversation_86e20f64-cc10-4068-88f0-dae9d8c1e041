package com.qm.ep.rebatecalc.config;

import cn.hutool.core.util.StrUtil;
import com.qm.ep.rebatecalc.ds.constant.Constants;
import com.qm.ep.rebatecalc.ds.decorator.HeaderContextHolder;
import feign.RequestInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class AsyncFeignConfig {

    @Bean
    public RequestInterceptor requestInterceptor() {
        return requestTemplate -> {
            String tenantId = getTenantId();
            if(StrUtil.isNotBlank(tenantId)) {
                requestTemplate.header(Constants.TENANT_ID, tenantId);
            }else {
                log.error("异步Feign获取请求头失败！");
            }
        };
    }

    private String getTenantId() {
        String tenantId = null;
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null != attributes) {
            HttpServletRequest request = attributes.getRequest();
            tenantId = request.getParameter(Constants.TENANT_ID);
            if (StringUtils.isBlank(tenantId)) {
                tenantId = request.getHeader(Constants.TENANT_ID);
            }
        }
        if(null == tenantId) {
            tenantId = HeaderContextHolder.getHeader(Constants.TENANT_ID);
        }

        return tenantId;
    }
}
