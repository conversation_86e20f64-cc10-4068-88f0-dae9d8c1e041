package com.qm.ep.rebatecalc.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("SYS_CONFIG")
@Schema(description = "系统参数表对象")
public class SystemConfigDO implements Serializable {
    @Schema(description = "序列化id")
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "系统参数代码")
    @TableField("code")
    private String code;

    @Schema(description = "系统参数描述")
    @TableField("`desc`")
    private String desc;

    @Schema(description = "系统参数值")
    @TableField("value")
    private String value;

    @Schema(description = "创建者")
    @TableField(value = "createBy", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建日期")
    @TableField(value = "createOn", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "更新者")
    @TableField(value = "updateBy", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "更新日期")
    @TableField(value = "updateOn", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT_UPDATE)
    private Date dtstamp;
}
