package com.qm.ep.rebatecalc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.qm.ep.rebatecalc.constant.Constants;
import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.bean.ExecTrialCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcDTO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcLogStructureDTO;
import com.qm.ep.rebatecalc.enumerate.CalcTypeEnum;
import com.qm.ep.rebatecalc.enumerate.ExecTypeEnum;
import com.qm.ep.rebatecalc.enumerate.StateEnum;
import com.qm.ep.rebatecalc.monitor.threadpool.report.redis.RedisService;
import com.qm.ep.rebatecalc.remote.RebateBaseRemote;
import com.qm.ep.rebatecalc.service.*;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class ExecCalcServiceImpl implements ExecCalcService {

    @Autowired
    private RedisService redisService;

    @Resource
    ExecTrialCalcService execTrialCalcService;

    @Resource
    ExecFormalCalcService execFormalCalcService;

    @Resource
    private ExecTrialCalcHistoryService execTrialCalcHistoryService;

    @Resource
    private ExecFormalCalcHistoryService execFormalCalcHistoryService;

    @Resource
    private ExecCalcLogService execCalcLogService;

    @Resource
    private RebateBaseRemote rebateBaseRemote;

    @Value("${calc.ttl:1440}")
    private long calcTTL;

    /**
     * 试算准备
     *
     * @param execCalcDTO 参数
     * @return 返回
     */
    @Override
    public String prepareCalc(ExecCalcDTO execCalcDTO) {
        String historyId = "";
        switch (execCalcDTO.getCalcType()) {
            case TRIAL:
                historyId = execTrialCalcService.prepareCalc(execCalcDTO);
                break;
            case FORMAL:
                historyId = execFormalCalcService.prepareCalc(execCalcDTO);
                break;
            default:
                break;
        }
        return historyId;
    }

    /**
     * 发起计算对象试算
     *
     * @param historyId 参数
     */
    @Override
    public void execCalc(CalcTypeEnum calcType, String historyId,String lockKey) throws InterruptedException {
        switch (calcType) {
            case TRIAL:
                execTrialCalcService.execCalc(historyId,lockKey);
                break;
            case FORMAL:
                execFormalCalcService.execCalc(historyId,lockKey);
                break;
            default:
                break;
        }
    }

    @Override
    public JsonResultVo<String> startCalc(ExecCalcDTO execCalcDTO) {
        JsonResultVo<String> ret = new JsonResultVo<>();

        // 查看当前时间是否在0点到早上6点之间，如果在这段时间不允许做试算，返回提示信息
        // 获取当前时间
        LocalTime now = LocalTime.now();
        LocalTime startTime = LocalTime.of(4, 0); // 00:00
        LocalTime endTime = LocalTime.of(6, 0);   // 06:00
        // 判断当前时间是否在 00:00 到 06:00 之间（包含 00:00，不包含 06:00）
        if (now.isAfter(startTime) && now.isBefore(endTime)) {
            ret.setMsgErr("当前时间为 " + now.format(DateTimeFormatter.ofPattern("HH:mm:ss")) + "，处于早上4点到早上6点之间，不允许进行试算操作。");
            // 可以在这里抛出异常或返回错误码，根据实际需求处理
            return ret; // 结束程序或方法
        }

        String historyId = "";
        String lockKey = CollUtil.join(Arrays.asList(execCalcDTO.getPolicyId(), execCalcDTO.getObjectId(), execCalcDTO.getObjectType(), execCalcDTO.getCalcType()), ":");
        // 不能采用Redission，因为需要在其他的线程进行锁释放
        // RLock lock = redissonClient.getLock(lockKey);

        boolean isLock = redisService.tryLock(lockKey, calcTTL, TimeUnit.MINUTES);

        if (!isLock) {
            ret.setMsgErr("正在进行试算！请计算完成后重试！");
            return ret;
        }
        try {
            log.info("线程{}获取到了分布式锁{}", Thread.currentThread().getName(),lockKey);
            // 业务处理
            historyId = prepareCalc(execCalcDTO);
            if (StrUtil.isBlank(historyId)) {
                ret.setMsgErr("生成历史记录出错！请稍后重试！");
                redisService.unlock(lockKey);
                return ret;
            }
            try {
                execCalc(execCalcDTO.getCalcType(), historyId,lockKey);
                ret.setMsg(execCalcDTO.getCalcType().getDesc() + "已发起");
            } catch (Exception e) {
                redisService.unlock(lockKey);
                String errorLog = execCalcDTO.getCalcType().getDesc() + "出错：" + e.getMessage();
                log.error(errorLog);
                switch (execCalcDTO.getCalcType()) {
                    case TRIAL:
                        ExecTrialCalcHistoryDO execTrialCalcHistoryDO = execTrialCalcHistoryService.getById(historyId);
                        execTrialCalcHistoryDO.setLog(execCalcLogService.append(execTrialCalcHistoryDO.getLog(),
                                ExecCalcLogStructureDTO.builder().content(errorLog).status(Constants.ERROR_STATUS).build()));
                        execTrialCalcHistoryDO.setState(StateEnum.ERROR);
                        execTrialCalcHistoryDO.setEnd(DateUtils.getSysdateTime());
                        execTrialCalcHistoryService.updateById(execTrialCalcHistoryDO);
                        break;
                    case FORMAL:
                        ExecFormalCalcHistoryDO execFormalCalcHistoryDO = execFormalCalcHistoryService.getById(historyId);
                        execFormalCalcHistoryDO.setLog(execCalcLogService.append(execFormalCalcHistoryDO.getLog(),
                                ExecCalcLogStructureDTO.builder().content(errorLog).status(Constants.ERROR_STATUS).build()));
                        execFormalCalcHistoryDO.setState(StateEnum.ERROR);
                        execFormalCalcHistoryDO.setEnd(DateUtils.getSysdateTime());
                        execFormalCalcHistoryService.updateById(execFormalCalcHistoryDO);
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            redisService.unlock(lockKey);
            log.warn("准备计算历史记录出错！", e);
        }

        return ret;
    }

    /**
     * 计算所有符合条件的政策的阶梯
     */
    @Override
    @DS(DataSourceType.W)
    public void calcLadder() {
        JsonResultVo<List<ExecCalcDTO>> ret = rebateBaseRemote.getLadderExecCalcList();
        for (ExecCalcDTO execCalcDTO : ret.getData()) {
            execCalcDTO.setExecType(ExecTypeEnum.AUTO);
            execCalcDTO.setCalcType(CalcTypeEnum.FORMAL);
            startCalc(execCalcDTO);
        }
    }
}
