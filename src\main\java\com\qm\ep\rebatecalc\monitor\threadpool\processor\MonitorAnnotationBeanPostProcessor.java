package com.qm.ep.rebatecalc.monitor.threadpool.processor;

import com.qm.ep.rebatecalc.monitor.annotation.Monitor;
import com.qm.ep.rebatecalc.monitor.threadpool.config.MonitorCache;
import com.qm.ep.rebatecalc.monitor.threadpool.executor.ExecutorPeekRecordHolder;
import com.qm.ep.rebatecalc.monitor.threadpool.job.AsyncConsumer;
import com.qm.ep.rebatecalc.monitor.threadpool.job.RealTimeInfoWatcher;
import com.qm.ep.rebatecalc.monitor.threadpool.job.TaskTimesWatcher;
import com.qm.ep.rebatecalc.monitor.threadpool.job.ThreadPoolWatcher;
import com.qm.ep.rebatecalc.monitor.threadpool.report.Reporter;
import com.qm.ep.rebatecalc.monitor.threadpool.report.redis.RedisAsyncReporter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Slf4j
public class MonitorAnnotationBeanPostProcessor implements ApplicationContextAware, ApplicationListener<ContextRefreshedEvent> {

    @Value("${spring.application.name:defaultName:noBodyKnowApplication}")
    private String applicationName;
    @Value("${server.port:8080}")
    private Integer port;

    private ApplicationContext applicationContext;

    private List<MonitorProcessor> processors = new ArrayList<>();

    private final ScheduledExecutorService scheduledExecutorService = new ScheduledThreadPoolExecutor(1);

    private Reporter reporter;

    private ExecutorService jobPool = Executors.newFixedThreadPool(10);

    public void addProcessor(MonitorProcessor processor) {
        processors.add(processor);
    }

    private void process(Object bean) {
        processors.forEach(processor -> processor.process(bean));
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext() == this.applicationContext) {
            Map<String, Object> beans = applicationContext.getBeansWithAnnotation(Monitor.class);
            for (Object bean : beans.values()) {
                this.process(bean);
            }

            this.reporter = new RedisAsyncReporter();
            initCommonCache();
            initPeekRecordHolder();

            initThreadPoolInfoWatcher(); //负责采集线程池的每15秒的activeCount和queueSize数据
            initTaskTimesWatcher(); //负责采集线程池的每15秒请求量
            initThreadPoolRealTimeWatcher(); //负责采集线程池每秒的数据信息，实时变化
            initAsyncConsumer();
//            initRefreshProperties();
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    private void initCommonCache() {
        MonitorCache.port = this.port;
        MonitorCache.applicationName = this.applicationName;
        MonitorCache.initTaskTimesPerMinuter();
    }

    private void initPeekRecordHolder() {
        for (String poolName : MonitorCache.executorMap.keySet()) {
            MonitorCache.peekRecordHolderMap.put(poolName,new ExecutorPeekRecordHolder());
        }
    }

    //初始化线程池上报任务
    private void initThreadPoolInfoWatcher() {
        jobPool.execute(new ThreadPoolWatcher());
    }

    //初始化线程池每15秒的请求量监控任务
    private void initTaskTimesWatcher() {
        jobPool.execute(new TaskTimesWatcher());
    }

    //初始化线程池实时数据监控
    private void initThreadPoolRealTimeWatcher() {
        jobPool.execute(new RealTimeInfoWatcher());
    }

    private void initAsyncConsumer() {
        jobPool.execute(new AsyncConsumer());
    }

}
