package com.qm.ep.rebatecalc.remote;

import com.qm.ep.rebatecalc.config.AsyncFeignConfig;
import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.dto.*;
import com.qm.ep.rebatecalc.domain.vo.AimDecomposeHistoryVO;
import com.qm.ep.rebatecalc.domain.vo.SqlStructureVO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@FeignClient(contextId = "tds-service-rebate",name = "tds-service-rebate", configuration = AsyncFeignConfig.class)
public interface RebateBaseRemote {

    /**
     * 获取sql结构
     * @param calcFactorDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/calcObject/getSqlStructure", produces = "application/json")
    JsonResultVo<SqlStructureVO> getSqlStructure(@RequestBody CalcObjectDTO calcFactorDTO);

    /**
     * 商务政策详细信息
     * @param policyDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/policy/detail", produces = "application/json")
    JsonResultVo<PolicyDTO> getPolicyDetail(@RequestBody PolicyDTO policyDTO);

    /**
     * 获取业务目标分解列表
     * @param aimDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/aim/getAimDetailList", produces = "application/json")
    JsonResultVo<List<AimDetailDTO>> getAimDetailList(@RequestBody AimDTO aimDTO);

    /**
     * 获取业务目标分解列表
     * @param aimDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/aim/detail/getLockedHistory", produces = "application/json")
    JsonResultVo<AimDecomposeHistoryVO> getLockedHistory(@RequestBody AimDTO aimDTO);

    /**
     * 将商务政策置为“已计算”
     * @param policyDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/policy/calculated", produces = "application/json")
    JsonResultVo<Object> calculated(@RequestBody PolicyDTO policyDTO);

    /**
     * 底表转换
     * @param turnBusinessBottomTableDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/turnBusinessBottomTable/turnBusinessBottomTableVerification", produces = "application/json")
    JsonResultVo<String> turnBusinessBottomTable(@RequestBody TurnBusinessBottomTableDTO turnBusinessBottomTableDTO);

    /**
     * 获取可以进行阶梯计算的政策对象列表
     * @return 返回
     */
    @PostMapping(value = "/calcObject/getLadderExecCalcList", produces = "application/json")
    JsonResultVo<List<ExecCalcDTO>> getLadderExecCalcList();

    /**
     * 获取系统参数
     * @param code 参数
     * @return 返回
     */
    @PostMapping(value = "/systemConfig/getValueByCode", produces = "application/json")
    JsonResultVo<String> getValueByCode(@RequestBody String code);

    /**
     * 获取底表结构
     * @param tableName 参数
     * @return 返回
     */
    @GetMapping(value = "/businessConstruction/getDataColumnAndId", produces = "application/json")
    JsonResultVo<BusinessConstructionDTO> getDataColumnAndId(@RequestParam String tableName);

    /**
     * 根据年月删除数据
     * @param tableName 参数
     * @param yesrs 参数
     * @return 返回
     */
    @PostMapping(value = "/businessData/deleteByMap", produces = "application/json")
    JsonResultVo<String> deleteByMap(@RequestParam("tableName") String tableName, @RequestBody String yesrs);

    @PostMapping(value = "/sysPersonOrg/checkPersonAtTopLevel", produces = "application/json")
    JsonResultVo<Boolean> checkPersonAtTopLevel(@RequestBody String personId);

    @PostMapping(value = "/sysPersonOrg/getLeafOrg", produces = "application/json")
    JsonResultVo<List<String>> getLeafOrg(@RequestBody String personId);

    @PostMapping(value = "/entryAccount/autoApply", produces = "application/json")
    JsonResultVo<Object> autoApply(@RequestBody ExecFormalCalcHistoryDO execCalcHistoryDO);
}
