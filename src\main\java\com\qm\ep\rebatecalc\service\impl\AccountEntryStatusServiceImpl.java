package com.qm.ep.rebatecalc.service.impl;


import com.qm.ep.rebatecalc.domain.bean.AccountEntryStatusDO;
import com.qm.ep.rebatecalc.mapper.AccountEntryStatusMapper;
import com.qm.ep.rebatecalc.service.AccountEntryStatusService;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AccountEntryStatusServiceImpl extends QmBaseServiceImpl<AccountEntryStatusMapper, AccountEntryStatusDO> implements AccountEntryStatusService {

}