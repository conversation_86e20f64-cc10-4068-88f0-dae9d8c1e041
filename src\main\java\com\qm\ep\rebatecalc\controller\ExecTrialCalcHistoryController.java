package com.qm.ep.rebatecalc.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatecalc.domain.bean.ExecTrialCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.bean.ExecTrialCalcResultDO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcHistoryDTO;
import com.qm.ep.rebatecalc.domain.dto.TurnBusinessBottomTableDTO;
import com.qm.ep.rebatecalc.domain.vo.SqlStructureVO;
import com.qm.ep.rebatecalc.enumerate.StateEnum;
import com.qm.ep.rebatecalc.mapper.ExecTrialCalcHistoryMapper;
import com.qm.ep.rebatecalc.remote.RebateBaseRemote;
import com.qm.ep.rebatecalc.service.ExecTrialCalcHistoryService;
import com.qm.ep.rebatecalc.service.ExecTrialCalcResultService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.JSONUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/execTrialCalcHistory")
@Tag(name = "计算对象试算历史", description = "[author:50013723]")
@Slf4j
public class ExecTrialCalcHistoryController extends BaseController {
    @Resource
    private ExecTrialCalcHistoryMapper execTrialCalcHistoryMapper;

    private static final String BEFORE_DAY_KEY = "beforeDay";
    private static final String LEAVE_VERSION_COUNT = "leaveVersionCount";
    private static final String PER_REQ_DEAL_CALC_HISTORY_COUNT = "perReqDealCount";

    @Resource
    private ExecTrialCalcHistoryService execCalcHistoryService;

    @Resource
    private ExecTrialCalcResultService execCalcResultService;

    @Autowired
    private RebateBaseRemote rebateBaseRemote;

    @Operation(summary = "查询计算对象试算历史列表", description= "[author:50013723]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ExecTrialCalcHistoryDO>> table(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        QmQueryWrapper<ExecTrialCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(BootAppUtil.isnotNullOrEmpty(execCalcHistoryDTO.getPolicyId()), ExecTrialCalcHistoryDO::getPolicyId, execCalcHistoryDTO.getPolicyId())
                .eq(ExecTrialCalcHistoryDO::getObjectId, execCalcHistoryDTO.getObjectId())
                .eq(ExecTrialCalcHistoryDO::getObjectType, execCalcHistoryDTO.getObjectType());
        QmPage<ExecTrialCalcHistoryDO> list = execCalcHistoryService.table(queryWrapper, execCalcHistoryDTO);
        JsonResultVo<QmPage<ExecTrialCalcHistoryDO>> ret = new JsonResultVo<>();
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "查询计算对象试算字段列表", description= "[author:50013723]")
    @PostMapping("/fields")
    public JsonResultVo<List<String>> getFields(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        QmQueryWrapper<ExecTrialCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(ExecTrialCalcHistoryDO::getId, execCalcHistoryDTO.getId());

        ExecTrialCalcHistoryDO execCalcHistoryDO = execCalcHistoryService.getOne(lambdaWrapper);
        if(execCalcHistoryDO==null) {
            throw new QmException("未找到试算历史记录！");
        }
        JsonResultVo<List<String>> ret = new JsonResultVo<>();
        SqlStructureVO sqlStructureVO = JSONUtils.packingDOFromJsonStr(execCalcHistoryDO.getSqlStructure(), SqlStructureVO.class);
        ret.setData(sqlStructureVO.getFields());
        return ret;
    }

    @Operation(summary = "删除计算对象试算历史记录", description= "[author:50013723]")
    @PostMapping("/deleteList")
    public JsonResultVo<String> deleteList(@RequestBody List<String> deleteIds) {
        if(deleteIds.isEmpty()) {
            throw new QmException("未选择删除记录！");
        }
        execCalcHistoryService.removeByIds(deleteIds);

        QmQueryWrapper<ExecTrialCalcResultDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcResultDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.in(ExecTrialCalcResultDO::getHistoryId, deleteIds);
        execCalcResultService.remove(lambdaWrapper);
        JsonResultVo<String> ret = new JsonResultVo<>();
        ret.setMsg("删除成功");
        return ret;
    }

    @Operation(summary = "通过计算对象ID，删除计算因子试算历史记录", description= "[author:50013723]")
    @PostMapping("/deleteByCalcObjectIds")
    public JsonResultVo<String> deleteByCalcObjectIds(@RequestBody List<String> calcObjectIds) {
        if(calcObjectIds.isEmpty()) {
            throw new QmException("未选择删除记录！");
        }
        QmQueryWrapper<ExecTrialCalcHistoryDO> queryHistoryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcHistoryDO> lambdaHistoryWrapper = queryHistoryWrapper.lambda();
        lambdaHistoryWrapper.in(ExecTrialCalcHistoryDO::getObjectId, calcObjectIds);
        lambdaHistoryWrapper.select(ExecTrialCalcHistoryDO.class, fieldInfo->"ID".equalsIgnoreCase(fieldInfo.getColumn()));
        List<String> historyIds = execCalcHistoryService.list(lambdaHistoryWrapper).stream().map(ExecTrialCalcHistoryDO::getId).collect(Collectors.toList());

        if(!historyIds.isEmpty()) {
            QmQueryWrapper<ExecTrialCalcResultDO> queryResultWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<ExecTrialCalcResultDO> lambdaResultWrapper = queryResultWrapper.lambda();
            lambdaResultWrapper.in(ExecTrialCalcResultDO::getHistoryId, historyIds);
            execCalcResultService.remove(lambdaResultWrapper);
        }
        execCalcHistoryService.remove(lambdaHistoryWrapper);
        JsonResultVo<String> ret = new JsonResultVo<>();
        ret.setMsg("删除成功");
        return ret;
    }

    @Operation(summary = "通过政策id，删除计算因子试算历史记录", description= "[author:50013723]")
    @PostMapping("/deleteByHistoryPolicyId")
    public boolean deleteByHistoryPolicyId(@RequestBody String policyId) {
        QmQueryWrapper<ExecTrialCalcHistoryDO> historyWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcHistoryDO> historylambdaWrapper = historyWrapper.lambda();
        historylambdaWrapper.eq(ExecTrialCalcHistoryDO::getPolicyId, policyId);
        return execCalcHistoryService.remove(historylambdaWrapper);
    }

    @Operation(summary = "按照时间点，清除过期数据", description= "[author:50013723]")
    @PostMapping("/deleteExpiredHistoriesByBeforeDay")
    public JsonResultVo<String> deleteExpiredHistoriesByBeforeDay(@RequestBody Map<String, Long> params) {
        JsonResultVo<String> ret = new JsonResultVo<>();
        String beforeDayStr = String.valueOf(params.get(BEFORE_DAY_KEY));
        if(!NumberUtil.isLong(beforeDayStr)) {
            ret.setMsgErr("无法解析 "+ BEFORE_DAY_KEY +"！不是合法的整数！");
            return ret;
        }
        LocalDateTime now = LocalDateTime.now();
        long beforeDay = NumberUtil.parseLong(beforeDayStr);
        LocalDateTime beforeDate = now.minusDays(beforeDay);

        QmQueryWrapper<ExecTrialCalcHistoryDO> queryHistoryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcHistoryDO> lambdaHistoryWrapper = queryHistoryWrapper.lambda();
        lambdaHistoryWrapper.ne(ExecTrialCalcHistoryDO::getState, StateEnum.PROCESS)
                .lt(ExecTrialCalcHistoryDO::getCreateOn, beforeDate);
        List<ExecTrialCalcHistoryDO> histories = execCalcHistoryService.list(lambdaHistoryWrapper);

        if(CollUtil.isNotEmpty(histories)) {
            List<String> historyIds = histories.stream().map(ExecTrialCalcHistoryDO::getId).collect(Collectors.toList());

            QmQueryWrapper<ExecTrialCalcResultDO> queryResultWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<ExecTrialCalcResultDO> lambdaResultWrapper = queryResultWrapper.lambda();
            lambdaResultWrapper.in(ExecTrialCalcResultDO::getHistoryId, historyIds);

            execCalcResultService.remove(lambdaResultWrapper);
            execCalcHistoryService.remove(lambdaHistoryWrapper);
        }
        ret.setMsg("清除过期试算历史成功！");
        return ret;
    }

    @Operation(summary = "按照版本，清除过期数据", description= "[author:50013723]")
    @PostMapping("/deleteExpiredHistoriesByVersion")
    public JsonResultVo<String> deleteExpiredHistoriesByVersion(@RequestBody Map<String, Long> params) {
        log.info("开始清除过期试算历史！参数：{}",JSONUtils.beanToJson(params));
        JsonResultVo<String> ret = new JsonResultVo<>();
        int leaveVersionCount = 3;
        int perReqDealCalcHistoryCount = 100;
        if(params.containsKey(LEAVE_VERSION_COUNT)) {
            try {
                leaveVersionCount = Integer.parseInt(String.valueOf(params.get(LEAVE_VERSION_COUNT)));
            } catch (Exception e) {
                log.error("获取参数{}出错！无法解析！", LEAVE_VERSION_COUNT);
            }
        }
        if(params.containsKey(PER_REQ_DEAL_CALC_HISTORY_COUNT)) {
            try {
                perReqDealCalcHistoryCount = Integer.parseInt(String.valueOf(params.get(PER_REQ_DEAL_CALC_HISTORY_COUNT)));
            } catch (Exception e) {
                log.error("获取参数{}出错！无法解析！", PER_REQ_DEAL_CALC_HISTORY_COUNT);
            }
        }
        QmQueryWrapper<ExecTrialCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        String objectIdColumnName = ReflectUtil.getField(ExecTrialCalcHistoryDO.class, "objectId").getAnnotation(TableField.class).value();
        lambdaWrapper.ne(ExecTrialCalcHistoryDO::getState, StateEnum.PROCESS).groupBy(ExecTrialCalcHistoryDO::getObjectId).having("count("+objectIdColumnName+")>{0}", leaveVersionCount).last("limit "+perReqDealCalcHistoryCount);
        List<String> objectIds = execCalcHistoryService.list(lambdaWrapper).stream().map(ExecTrialCalcHistoryDO::getObjectId).collect(Collectors.toList());
        int finalLeaveVersionCount = leaveVersionCount;
        objectIds.forEach(i->{
            QmQueryWrapper<ExecTrialCalcHistoryDO> queryHistoryWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<ExecTrialCalcHistoryDO> lambdaHistoryWrapper = queryHistoryWrapper.lambda();
            lambdaHistoryWrapper.eq(ExecTrialCalcHistoryDO::getObjectId, i).ne(ExecTrialCalcHistoryDO::getState, StateEnum.PROCESS).orderByDesc(ExecTrialCalcHistoryDO::getCalcVersion).last("limit "+ finalLeaveVersionCount +",1000000");
            List<String> historyIds = execCalcHistoryService.list(lambdaHistoryWrapper).stream().map(ExecTrialCalcHistoryDO::getId).collect(Collectors.toList());
            // List<String> ids = new ArrayList<>();
            // historyIds.forEach(historyId->{
            //     List<String> resultIds=execTrialCalcHistoryMapper.selectIdsByHistoryId(historyId);
            //     ids.addAll(resultIds);
            // });
            execCalcHistoryService.removeTrailHistoryAndResultTransactional(historyIds);
            log.info("清除过期试算历史成功！历史数量：{},结果数量：{}",historyIds.size());
        });

        ret.setMsg("清除过期试算历史成功！");
        return ret;
    }

    @Operation(summary = "计算结果转业务底表", description= "[author:50013723]")
    @PostMapping("/turnBusinessBottomTable")
    public JsonResultVo<String> turnBusinessBottomTable(@RequestBody TurnBusinessBottomTableDTO turnBusinessBottomTableDTO) {
        ExecTrialCalcHistoryDO execTrialCalcHistoryDO = execCalcHistoryService.getById(turnBusinessBottomTableDTO.getId());
        execTrialCalcHistoryDO.setState(StateEnum.CONVERTING);
        execCalcHistoryService.updateById(execTrialCalcHistoryDO);
        turnBusinessBottomTableDTO.setStructure(execTrialCalcHistoryDO.getSqlStructure());
        JsonResultVo<String> reportSqlStructure = rebateBaseRemote.turnBusinessBottomTable(turnBusinessBottomTableDTO);
        ExecTrialCalcHistoryDO execTrialCalcHistory = execCalcHistoryService.getById(turnBusinessBottomTableDTO.getId());
        execTrialCalcHistory.setState(StateEnum.FINISH);
        execCalcHistoryService.updateById(execTrialCalcHistory);
        return reportSqlStructure;
    }

    @Operation(summary = "获取最新的已计算完成的试算历史信息", description= "[author:50013723]")
    @PostMapping("/latestCompletedHistory")
    public JsonResultVo<ExecTrialCalcHistoryDO> getLatestCompletedHistory(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        QmQueryWrapper<ExecTrialCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(StrUtil.isNotBlank(execCalcHistoryDTO.getPolicyId()), ExecTrialCalcHistoryDO::getPolicyId, execCalcHistoryDTO.getPolicyId())
                .eq(ExecTrialCalcHistoryDO::getObjectId, execCalcHistoryDTO.getObjectId())
                .eq(ExecTrialCalcHistoryDO::getObjectType, execCalcHistoryDTO.getObjectType())
                .eq(ExecTrialCalcHistoryDO::getState, StateEnum.FINISH)
                .orderByDesc(ExecTrialCalcHistoryDO::getCalcVersion).last("limit 1");
        ExecTrialCalcHistoryDO execTrialCalcHistoryDO = execCalcHistoryService.getOne(lambdaWrapper);
        JsonResultVo<ExecTrialCalcHistoryDO> ret = new JsonResultVo<>();
        ret.setData(execTrialCalcHistoryDO);
        return ret;
    }
}
