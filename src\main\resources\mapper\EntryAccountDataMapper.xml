<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatecalc.mapper.EntryAccountDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.rebatecalc.domain.bean.EntryAccountDataDO">
        <id column="id" property="id" />
        <result column="uniqueKey" property="uniqueKey" />
        <result column="classItem" property="classItem" />
        <result column="auditType" property="auditType" />
        <result column="billNo" property="billNo" />
        <result column="series" property="series" />
        <result column="dealerCode" property="dealerCode" />
        <result column="dealerName" property="dealerName" />
        <result column="rebateAmount" property="rebateAmount" />
        <result column="remark" property="remark" />
        <result column="operatorId" property="operatorId" />
        <result column="stateCode" property="stateCode" />
        <result column="policyId" property="policyId" />
        <result column="policyCode" property="policyCode" />
        <result column="policyName" property="policyName" />
        <result column="taskinsanceCode" property="taskinsanceCode" />
        <result column="taskFlowinstanceCode" property="taskFlowinstanceCode" />
        <result column="processInstanceId" property="processInstanceId" />
        <result column="CREATEBY" property="createby" />
        <result column="CREATEON" property="createon" />
        <result column="UPDATEBY" property="updateby" />
        <result column="UPDATEON" property="updateon" />
        <result column="submitBy" property="submitBy" />
        <result column="submitCode" property="submitCode" />
        <result column="DTSTAMP" property="dtstamp" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    id, uniqueKey, classItem, auditType, billNo, series, dealerCode, dealerName, rebateAmount, remark, operatorId, stateCode, policyId, policyCode, policyName, taskinsanceCode, taskFlowinstanceCode, processInstanceId, CREATEBY, CREATEON, UPDATEBY, UPDATEON, submitBy, submitCode, DTSTAMP
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
            select
                a.uniqueKey,
                a.classItem,
                a.auditType,
                a.billNo,
                a.series,
                a.dealerCode,
                a.dealerName,
                a.rebateAmount,
                a.remark,
                a.operatorId,
                a.stateCode,
                a.policyId,
                a.policyCode,
                a.policyName,
                a.taskinsanceCode,
                a.taskFlowinstanceCode,
                a.processInstanceId,
                a.CREATEBY,
                a.CREATEON,
                a.UPDATEBY,
                a.UPDATEON,
                a.submitBy,
                a.submitCode,
                a.DTSTAMP,
                a.id
            from entry_account_data a
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.rebatecalc.domain.bean.EntryAccountDataDO">
        <include refid="QuerySQL" /> where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.rebatecalc.domain.bean.EntryAccountDataDO">
        <include refid="QuerySQL" />
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=","> #{item} </foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.rebatecalc.domain.bean.EntryAccountDataDO">
        <include refid="QuerySQL" />
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null"> ${k} IS NULL </when>
                        <otherwise> ${k} = #{v} </otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.rebatecalc.domain.bean.EntryAccountDataDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from ( <include refid="QuerySQL" />${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.rebatecalc.domain.bean.EntryAccountDataDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.rebatecalc.domain.bean.EntryAccountDataDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.rebatecalc.domain.bean.EntryAccountDataDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.rebatecalc.domain.bean.EntryAccountDataDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.rebatecalc.domain.bean.EntryAccountDataDO">
        <include refid="QuerySQL" />${ew.customSqlSegment}
    </select>
</mapper>
