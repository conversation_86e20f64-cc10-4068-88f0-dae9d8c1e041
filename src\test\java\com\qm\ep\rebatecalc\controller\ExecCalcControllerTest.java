package com.qm.ep.rebatecalc.controller;

import com.qm.ep.rebatecalc.domain.dto.ExecCalcDTO;
import com.qm.ep.testapi.constant.UserConstants;
import com.qm.ep.testapi.controller.BaseTestController;
import lombok.extern.slf4j.Slf4j;
import nl.jqno.equalsverifier.EqualsVerifier;
import nl.jqno.equalsverifier.Warning;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class ExecCalcControllerTest extends BaseTestController<ExecCalcController> {

    @Before
    public void beforeMethod() {
        this.initUser(UserConstants.USER_CODE_COMPANY);
    }

    /**
     * 覆盖dto
     */
    @Test
    public void moduleDtoTest() {
        EqualsVerifier.simple().forClass(ExecCalcDTO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
    }

    @Test
    public void start() {
        try {
            ExecCalcDTO dto = this.moduleEmpty(ExecCalcDTO.class);
            this.testController.start(dto);
        } catch (Exception e) {
            Assert.assertEquals("政策ID或计算因子ID为空！", e.getMessage());
        }
    }
}