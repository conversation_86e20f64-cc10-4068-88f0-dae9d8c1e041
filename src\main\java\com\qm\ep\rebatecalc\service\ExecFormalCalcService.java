package com.qm.ep.rebatecalc.service;

import com.qm.ep.rebatecalc.domain.dto.ExecCalcDTO;
import org.springframework.scheduling.annotation.Async;

/**
 * <AUTHOR>
 */
public interface ExecFormalCalcService {

    /**
     * 计算准备
     * @param execCalcDTO 参数
     * @return 返回
     */
    String prepareCalc(ExecCalcDTO execCalcDTO);

    /**
     * 发起计算对象计算
     * @param historyId 计算历史ID
     * @throws InterruptedException 异常
     */
    @Async("execFormalCalcAsync")
    void execCalc(String historyId,String lockKey) throws InterruptedException;
}
