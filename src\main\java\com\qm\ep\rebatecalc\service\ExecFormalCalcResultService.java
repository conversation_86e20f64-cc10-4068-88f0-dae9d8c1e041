package com.qm.ep.rebatecalc.service;

import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcResultDO;
import com.qm.ep.rebatecalc.domain.bean.ExecTrialCalcResultDO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcResultDTO;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.IQmBaseService;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public interface ExecFormalCalcResultService extends IQmBaseService<ExecFormalCalcResultDO> {

    QmPage<ExecFormalCalcResultDO> queryCalcResult(ExecCalcResultDTO execCalcResultDTO);
    /**
     * 通过SQL 保存
     * @param map 参数
     * @return 返回
     */
    int saveBySql(Map<String, Object> map);

    /**
     * 通过SQL执行
     * @param map 参数
     * @return 返回
     */
    List<Map> getBySql(Map<String, Object> map);

    /**
     * 查询所有计算结果id
     * @param tempDTO 参数
     * @return 返回
     */
    List<Integer> getAllId(ExecFormalCalcResultDO tempDTO);
}
