package com.qm.ep.rebatecalc.monitor.threadpool.report;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ThreadPoolRealTimeInfo {

    private String poolName;
    private Integer maximumPoolSize;
    private Integer corePoolSize;
    private Integer activePoolSize;
    private Long keepAliveTime;
    private Integer queueCapacity;
    private Integer queueSize;
    private long errorTaskCount;
    private long completedTaskCount;
    private boolean preStartAllCoreThreads;
    private boolean preStartCoreThread;
    private String rejectedExecutionType;
    private String taskCountScoreThreshold;

}
