package com.qm.ep.rebatecalc.monitor.threadpool.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MonitorThreadPool {

    /**
     * 线程池名称
     *
     */
    String value();

    /**
     * 采集周期，单位毫秒
     *
     */
    long period() default 10000;

    /**
     * 初次采样延时，单位毫秒
     *
     */
    long initDelay() default 0;

}