package com.qm.ep.rebatecalc.service;

import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcHistoryDTO;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.IQmBaseService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface ExecFormalCalcHistoryService extends IQmBaseService<ExecFormalCalcHistoryDO> {

    @Async("autoEnterAccountAsync")
    void autoApplyEntryAccount(ExecFormalCalcHistoryDO execCalcHistoryDO);

    @Async("autoEnterAccountAsync")
    void autoApply(ExecFormalCalcHistoryDO execCalcHistoryDO);

    /**
     * 查询计算对象计算历史列表
     *
     * @param execCalcHistoryDTO exec计算历史数据
     * @return {@link QmPage }<{@link ExecFormalCalcHistoryDO }>
     */
    QmPage<ExecFormalCalcHistoryDO> tableList(ExecCalcHistoryDTO execCalcHistoryDTO);

    /**
     * 查询计算对象计算字段列表
     *
     * @param execCalcHistoryDTO exec计算历史数据
     * @return {@link List }<{@link String }>
     */
    List<String> getFields(ExecCalcHistoryDTO execCalcHistoryDTO);

    /**
     * 删除计算对象计算历史记录
     *
     * @param deleteIds 删除id
     */
    void deleteList(List<String> deleteIds);
}
