package com.qm.ep.rebatecalc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.qm.ep.rebatecalc.constant.Constants;
import com.qm.ep.rebatecalc.constant.MqConstants;
import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.dto.*;
import com.qm.ep.rebatecalc.domain.vo.AimDecomposeHistoryVO;
import com.qm.ep.rebatecalc.domain.vo.SqlStructureVO;
import com.qm.ep.rebatecalc.ds.decorator.HeaderContextHolder;
import com.qm.ep.rebatecalc.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebatecalc.enumerate.ExecTypeEnum;
import com.qm.ep.rebatecalc.enumerate.StateEnum;
import com.qm.ep.rebatecalc.mapper.ExecFormalCalcHistoryMapper;
import com.qm.ep.rebatecalc.monitor.threadpool.report.redis.RedisService;
import com.qm.ep.rebatecalc.remote.RebateBaseRemote;
import com.qm.ep.rebatecalc.service.ExecCalcLogService;
import com.qm.ep.rebatecalc.service.ExecFormalCalcHistoryService;
import com.qm.ep.rebatecalc.service.ExecFormalCalcResultService;
import com.qm.ep.rebatecalc.service.ExecFormalCalcService;
import com.qm.ep.rebatecalc.utils.SqlUtils;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.mq.builder.DefaultDestination;
import com.qm.tds.mq.builder.DefaultTxMessage;
import com.qm.tds.mq.builder.MessageStruct;
import com.qm.tds.mq.constant.ExchangeType;
import com.qm.tds.mq.message.MessageSendStruct;
import com.qm.tds.mq.remote.MqFeignRemote;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExecFormalCalcServiceImpl implements ExecFormalCalcService {

    @Autowired
    private RedisService redisService;

    private static final int MAX_RETRY_TIME = 50;

    @Value("${spring.application.name}")
    private String serviceName;

    @Resource
    private ExecCalcLogService execCalcLogService;

    @Resource
    RebateBaseRemote rebateBaseRemote;

    @Resource
    ExecFormalCalcHistoryService execFormalCalcHistoryService;

    @Resource
    ExecFormalCalcResultService execFormalCalcResultService;

    @Resource
    private MqFeignRemote mqFeignRemote;

    @Value("${rebate.exec-calc.result.max-amount:6000000}")
    private Long execCalcResultRecordMaxAmount;

    @Resource
    private ExecFormalCalcHistoryMapper execFormalCalcHistoryMapper;


    /**
     * 计算准备
     *
     * @param execCalcDTO 参数
     * @return 返回
     */
    @Override
    @Transactional(propagation= Propagation.REQUIRES_NEW)
    public String prepareCalc(ExecCalcDTO execCalcDTO) {
        log.info("准备计算执行：prepareCalc(ExecCalcDTO execCalcDTO={})开始",execCalcDTO);
        /*if(BootAppUtil.isNullOrEmpty(execCalcDTO.getPolicyId()) || BootAppUtil.isNullOrEmpty(execCalcDTO.getObjectId()) || BootAppUtil.isNullOrEmpty(execCalcDTO.getObjectType())) {
            throw new QmException("政策ID或计算对象ID或计算对象类型为空！");
        }*/
        PolicyDTO policyDTO = new PolicyDTO();
        policyDTO.setId(execCalcDTO.getPolicyId());
        // 方案合并计算，改变政策的状态
        if(CalcObjectTypeEnum.COMBINATION.equals(execCalcDTO.getObjectType())) {
            rebateBaseRemote.calculated(policyDTO);
        }

        CalcObjectDTO calcObjectDTO = new CalcObjectDTO();
        calcObjectDTO.setPolicyId(execCalcDTO.getPolicyId());
        calcObjectDTO.setObjectId(execCalcDTO.getObjectId());
        calcObjectDTO.setObjectType(execCalcDTO.getObjectType());
        calcObjectDTO.setAbbreviated(true);
        JsonResultVo<SqlStructureVO> sqlStructure = rebateBaseRemote.getSqlStructure(calcObjectDTO);

        LambdaQueryWrapper<ExecFormalCalcHistoryDO> execCalcHistoryWrapper = new QmQueryWrapper<ExecFormalCalcHistoryDO>().lambda();
        execCalcHistoryWrapper.eq(BootAppUtil.isnotNullOrEmpty(execCalcDTO.getPolicyId()), ExecFormalCalcHistoryDO::getPolicyId, execCalcDTO.getPolicyId())
                .eq(ExecFormalCalcHistoryDO::getObjectId, execCalcDTO.getObjectId())
                .eq(ExecFormalCalcHistoryDO::getObjectType, execCalcDTO.getObjectType())
                .orderByDesc(ExecFormalCalcHistoryDO::getCalcVersion).last("limit 1");
        ExecFormalCalcHistoryDO maxExecHistory = execFormalCalcHistoryService.getOne(execCalcHistoryWrapper);

        ExecFormalCalcHistoryDO execCalcHistoryDO = new ExecFormalCalcHistoryDO();
        execCalcHistoryDO.setPolicyId(execCalcDTO.getPolicyId());
        execCalcHistoryDO.setObjectId(execCalcDTO.getObjectId());
        execCalcHistoryDO.setObjectType(execCalcDTO.getObjectType());
        execCalcHistoryDO.setExecType(null==execCalcDTO.getExecType()? ExecTypeEnum.MANUAL:execCalcDTO.getExecType());
        execCalcHistoryDO.setCalcVersion(maxExecHistory==null ? 1 : maxExecHistory.getCalcVersion() + 1);
        execCalcHistoryDO.setState(StateEnum.PROCESS);
        execCalcHistoryDO.setBegin(DateUtils.getSysdateTime());
        execCalcHistoryDO.setSqlStructure(JSONUtils.beanToJson(sqlStructure.getData()));
        execCalcHistoryDO.setAutoTurnTable(execCalcDTO.getAutoTurnTable());
        execCalcHistoryDO.setTableName(execCalcDTO.getTableName());
        execCalcHistoryDO.setAutoEnterAccount(execCalcDTO.getAutoEnterAccount());

        List<ExecCalcLogStructureDTO> execCalcLogStructures = new ArrayList<>();
        execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("创建计算任务").build());
        execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行SQL：" + sqlStructure.getData().getTemporaryTableSqlFormatted() + "\n" + sqlStructure.getData().getSqlFormatted()).build());

        execCalcHistoryDO.setLog(execCalcLogService.append(null, execCalcLogStructures));

        // 如果模型中有组织和业务目标版本，需要通过它们找到STD和AAK拆解的锁定版本并记录到extra字段中
        // 目的：总体预算编制里，需要知道每个计算结果，是用哪个销售分解版本计算的
        JsonResultVo<PolicyDTO> policyDetail = rebateBaseRemote.getPolicyDetail(policyDTO);
        if(policyDetail.isOk()) {
            String orgCode = policyDetail.getData().getOrgCode();
            String aimVersion = policyDetail.getData().getAimVersion();
            if(StrUtil.isNotBlank(orgCode) && StrUtil.isNotBlank(aimVersion)) {
                AimDTO aimDTO = new AimDTO();
                aimDTO.setOrgCode(orgCode);
                aimDTO.setAimVersion(aimVersion);
                JsonResultVo<List<AimDetailDTO>> aimDetailList = rebateBaseRemote.getAimDetailList(aimDTO);
                if(aimDetailList.isOk()) {
                    Map<String, AimDecomposeHistoryVO> currentVersion = new HashMap<>(5);
                    aimDetailList.getData().forEach(detail->{
                        String category = detail.getCategory(); // 分解分类
                        if(StrUtil.isNotBlank(category)) {
                            // 获取 detail.getId() 对应历史记录的lock状态记录，然后提取版本
                            AimDTO aimDetail = new AimDTO();
                            aimDetail.setDetailId(detail.getId());
                            JsonResultVo<AimDecomposeHistoryVO> historyLockVersion = rebateBaseRemote.getLockedHistory(aimDetail);
                            if(historyLockVersion.isOk()) {
                                AimDecomposeHistoryVO history = historyLockVersion.getData();
                                currentVersion.put(category, history);
                            }
                        }
                    });
                    execCalcHistoryDO.setExtra(JSONUtil.toJsonStr(currentVersion));
                }
            }
        }

        execCalcHistoryDO.setDtstamp(DateUtils.getSysdateTime());
        execFormalCalcHistoryService.save(execCalcHistoryDO);
        log.info("准备计算执行：prepareCalc(ExecCalcDTO execCalcDTO={})结束",execCalcDTO);
        return execCalcHistoryDO.getId();
    }

    /**
     * 发起计算对象计算
     *
     * @param historyId 参数
     */
    @Override
    @DS(DataSourceType.W)
    @Transactional(rollbackFor = Exception.class)
    public void execCalc(String historyId,String lockKey) throws InterruptedException {
        log.info("正式计算执行==================：execCalc(String historyId={}) 开始",historyId);
        int retryCount = 0;
        ExecFormalCalcHistoryDO execCalcHistoryDO = null;
        while (retryCount < MAX_RETRY_TIME) {
            execCalcHistoryDO = execFormalCalcHistoryService.getById(historyId);
            if(execCalcHistoryDO!=null) {
                break;
            }
            retryCount++;
            TimeUnit.SECONDS.sleep(3);
        }
        List<ExecCalcLogStructureDTO> execCalcLogStructures = new ArrayList<>();
        if(execCalcHistoryDO==null) {
            log.error("获取当前计算历史{}日志出错，本次计算退出", historyId);
            return;
        } else {
            log.info("获取当前计算历史{}日志成功！尝试次数：{}", historyId, retryCount);
        }

        execCalcHistoryDO.setExecutorThreadName(Thread.currentThread().getName());

        //execFormalCalcHistoryMapper.updateExecutorThreadName(execCalcHistoryDO.getExecutorThreadName(),historyId);
        //execFormalCalcHistoryService.updateById(execCalcHistoryDO);
        //execCalcHistoryDO = execFormalCalcHistoryService.getById(historyId);

        try {
            String sqlStructureBase = execCalcHistoryDO.getSqlStructure();
            SqlStructureVO sqlStructure = JSONUtils.packingDOFromJsonStr(sqlStructureBase, SqlStructureVO.class);

            Map<String, Object> sqlMap = new HashMap<>(1);
            if(BootAppUtil.isnotNullOrEmpty(sqlStructure.getTemporaryTableSqlFormatted())) {
                sqlMap.put("sql", sqlStructure.getTemporaryTableSqlFormatted());
                execFormalCalcResultService.getBySql(sqlMap);
            }

            String sqlbase = "(" + sqlStructure.getSql() + ") as sqlbase";
            sqlMap.put("sql", "select count(*) as total from " + sqlbase);
            List<Map> countResult = execFormalCalcResultService.getBySql(sqlMap);
            long total = Long.parseLong(String.valueOf(countResult.get(0).get("total")));
            JsonResultVo<String> sysConfigResult = rebateBaseRemote.getValueByCode(Constants.RESULT_MAX_AMOUNT);
            long maxAmount = sysConfigResult.isOk() ? Long.valueOf(sysConfigResult.getData()) : execCalcResultRecordMaxAmount;
            if(CollUtil.isNotEmpty(countResult) && total<=maxAmount) {
                List<String> selectFields = sqlStructure.getFields();
                int fieldSize = selectFields.size();
                List<String> fields = new ArrayList<>();
                List<String> sumFields = new ArrayList<>();
                for (int i = 1; i <= fieldSize; i++) {
                    fields.add("field" + i);
                    sumFields.add("sum(" + SqlUtils.emphasis(SqlUtils.aliasName(true, selectFields.get(i-1))) + ") as field" + i);
                }
                String sql = " insert into exec_formal_calc_result(historyId,"+ StringUtils.join(fields, ",") +") \n" +
                        " select '" + historyId + "' as historyId,sqlbase.* from " + sqlbase;

                sqlMap.put("sql", sql);

                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行计算SQL语句").build());
                int insertCount = execFormalCalcResultService.saveBySql(sqlMap);
                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行计算成功，共生成"+insertCount+"条记录").build());

                // 对计算结果 求sum
                String sumSql = " select " + StringUtils.join(sumFields, ",") + " from " + sqlbase;
                sqlMap.put("sql", sumSql);
                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL语句：" + sumSql).build());
                List<Map> sumResult = execFormalCalcResultService.getBySql(sqlMap);
                if(CollUtil.isNotEmpty(sumResult)) {
                    String rs = JSONUtils.beanToJson(sumResult.get(0));
                    execCalcHistoryDO.setSumResult(rs);
                    execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 成功！结果为：" + rs).build());
                } else {
                    execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 失败！无结果").status(Constants.ERROR_STATUS).build());
                }

                execCalcHistoryDO.setState(StateEnum.FINISH);

                // 阶梯计算，不备份
                if(!CalcObjectTypeEnum.LADDER.equals(execCalcHistoryDO.getObjectType())) {
                    startMirrorBackUp(historyId, insertCount, execCalcHistoryDO);
                }
            } else {
                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("计算结果记录数"+total+"。由于记录数大于500万，本次计算终止！请检查配置是否正确！").status(Constants.ERROR_STATUS).build());
                execCalcHistoryDO.setState(StateEnum.ERROR);
            }

            // 删除临时表
            Map<CalcObjectTypeEnum, Map<String, String>> temporaryTableSqlMap = sqlStructure.getTemporaryTableSql();
            if(null!=temporaryTableSqlMap) {
                List<String> dropTemporaryTableSql = new ArrayList<>();
                temporaryTableSqlMap.values().forEach(s-> s.keySet().forEach(t-> dropTemporaryTableSql.add(" DROP TABLE IF EXISTS " + SqlUtils.emphasis(t) + " ; ")));
                if(CollUtil.isNotEmpty(dropTemporaryTableSql)) {
                    sqlMap.put("sql", StringUtils.join(dropTemporaryTableSql, "\n"));
                    execFormalCalcResultService.getBySql(sqlMap);
                }
            }

            // 转底表
            if("1".equals(execCalcHistoryDO.getAutoTurnTable())){
                List<BusinessConstructionDTO> businessConstruction =  rebateBaseRemote.getDataColumnAndId(execCalcHistoryDO.getTableName()).getDataList(); // 底表结构
                String yesrsFieldIndex = businessConstruction.stream().filter(item-> "年月".equals(item.getFieldName())).
                        map(BusinessConstructionDTO::getRelevanceFieldName).collect(Collectors.toList()).get(0);  // 年月是field几
                StringBuilder insertSql = new StringBuilder(" insert into businessdata( id,tablename, " ).append(yesrsFieldIndex);
                StringBuilder selectSql = new StringBuilder("select uuid() as id, '" + execCalcHistoryDO.getTableName() + "' as tablename ,TO_CHAR(CURRENT_DATE, 'YYYYMM') as " + yesrsFieldIndex);
                SqlStructureVO sqlStructureVO = JSONUtils.packingDOFromJsonStr(execCalcHistoryDO.getSqlStructure(), SqlStructureVO.class);
                List<String> resultFields = sqlStructureVO.getFields(); // 计算结果的字段
                for(int i=1 ;i<=resultFields.size();i++){
                    int index = i-1;
                    String fieldIndex = businessConstruction.stream().filter(item-> resultFields.get(index).equals(item.getFieldName()))
                            .map(BusinessConstructionDTO::getRelevanceFieldName).collect(Collectors.toList()).get(0);
                    insertSql.append(",").append(fieldIndex);
                    selectSql.append(",field").append(i).append(" as ").append(fieldIndex);
                }
                selectSql.append(" from exec_formal_calc_result where historyId = '").append(execCalcHistoryDO.getId()).append("' ");
                // 删除当前年月数据
                rebateBaseRemote.deleteByMap(execCalcHistoryDO.getTableName(),yesrsFieldIndex);
                Map<String, Object> businessMap = new HashMap<>(1);
                businessMap.put("sql", insertSql.append(")").append(selectSql));
                // 插入
                execFormalCalcResultService.saveBySql(businessMap);
            }
        } catch (Exception e) {
            execCalcHistoryDO.setState(StateEnum.ERROR);
            execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行计算出错\n"+e.getMessage()).status(Constants.ERROR_STATUS).build());
        } finally {
            redisService.unlock(lockKey);
            log.info("正式计算执行：execCalc(String historyId={}) 结束前",historyId);
            execCalcHistoryDO.setLog(execCalcLogService.append(execCalcHistoryDO.getLog(), execCalcLogStructures));
            execCalcHistoryDO.setEnd(DateUtils.getSysdateTime());

            //不更新SqlStructure
            execCalcHistoryDO.setSqlStructure(null);
            execFormalCalcHistoryService.updateById(execCalcHistoryDO);
            // 计算结束后 判断是否自动申请入账
            // 打印是否自动入账
            log.info("是否自动入账==================：{}",execCalcHistoryDO.getAutoEnterAccount());
            if("1".equals(execCalcHistoryDO.getAutoEnterAccount())){
                // 打印入参
                log.info("入账入参==================：{}",execCalcHistoryDO);
                //2023-09-25使用新的入账接口
                execFormalCalcHistoryService.autoApply(execCalcHistoryDO);
            }
            log.info("正式计算执行：execCalc(String historyId={}) 结束后",historyId);


        }
    }


    private void startMirrorBackUp(String historyId, int insertCount, ExecFormalCalcHistoryDO execCalcHistoryDO) {
        Map<String, Object> model = new HashMap<>(2);
        model.put("historyId", historyId);
        model.put("total", insertCount);
        LoginKeyDO loginKey = new LoginKeyDO();
        loginKey.setTenantId(HeaderContextHolder.getHeader("tenantId"));
        // 备份正式计算结果，通过mq发送
        MessageSendStruct messageSendStruct = new MessageSendStruct(
                DefaultTxMessage.builder()
                        .businessKey(IdWorker.getIdStr())
                        .businessModule(serviceName)
                        .content(MessageStruct.builder()
                                .requestInfo(loginKey)
                                .message(model)
                                .serviceName(serviceName)
                                .build())
                        .build(),
                DefaultDestination.builder()
                        .exchangeType(ExchangeType.DIRECT)
                        .exchangeName(MqConstants.MIRROR_FORMAL_CALC_RESULT_EXCHANGE)
                        .routingKey(MqConstants.MIRROR_FORMAL_CALC_RESULT_ROUTING_KEY)
                        .queueName("").build());
        try {
            execCalcHistoryDO.setMqState(StateEnum.PROCESS);
            JsonResultVo mqRet = mqFeignRemote.sendRabbitMq(messageSendStruct);
            if (mqRet.getCode() != HttpServletResponse.SC_OK) {
                // 记录个日志，防止mq公共服务突然宕机，造成数据丢失，以便于mq公共服务宕机时没有记录日志，修复成功后手动发送信息
                // mq没有宕机时，如果发送失败，不用手动处理，mq公共服务会按幂等算法自动重试5次
                execCalcHistoryDO.setMqState(StateEnum.ERROR);
                execCalcHistoryDO.setMqLog(mqRet.getMsg());
            }
        } catch (Exception e) {
            log.error("备份正式计算结果出错！{}", e.getMessage());
            execCalcHistoryDO.setMqState(StateEnum.ERROR);
            execCalcHistoryDO.setMqLog(e.getMessage());
        }
    }
}
