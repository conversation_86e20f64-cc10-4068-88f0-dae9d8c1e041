package com.qm.ep.rebatecalc.constant;

/**
 * 常量类
 * <AUTHOR>
 */
public class Constants {
    private Constants() {
    }

    /** 计算状态-待计算 */
    public static final Integer WAIT_CALCULATE = 0;
    /** 计算状态-计算完毕 */
    public static final Integer FINISH_CALCULATE = 1;
    /** 计算状态-计算中 */
    public static final Integer CALCULATING = 2;
    /** 计算状态-计算失败 */
    public static final Integer CALCULATE_FAILED = 3;

    public static final String SUMMARY_FIELD = "汇总字段";

    public static final String ERROR_STATUS = "error";
    public static final String SUCCESS_STATUS = "success";

    ///////////////// 系统参数代码 //////////////////////////
    public static final String RESULT_MAX_AMOUNT = "exec_calc_result_record_max_amount";

}
