package com.qm.ep.rebatecalc.domain.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("REPORT_DATA")
@Schema(description = "ReportData对象")
public class ReportDataDO {
    @Schema(description = "序列化id")
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    @Schema(description = "报表ID")
    @TableField("REPORT_ID")
    private String reportId;

    @Schema(description = "经销商代码")
    @TableField("DEALER_CODE")
    private String dealerCode;

    @Schema(description = "表名")
    @TableField("TABLE_NAME")
    private String tableName;

    @Schema(description = "数据表字段")
    @TableField("FIELD1")
    private String field1;

    @Schema(description = "数据表字段")
    @TableField("FIELD2")
    private String field2;

    @Schema(description = "数据表字段")
    @TableField("FIELD3")
    private String field3;

    @Schema(description = "数据表字段")
    @TableField("FIELD4")
    private String field4;

    @Schema(description = "数据表字段")
    @TableField("FIELD5")
    private String field5;

    @Schema(description = "数据表字段")
    @TableField("FIELD6")
    private String field6;

    @Schema(description = "数据表字段")
    @TableField("FIELD7")
    private String field7;

    @Schema(description = "数据表字段")
    @TableField("FIELD8")
    private String field8;

    @Schema(description = "数据表字段")
    @TableField("FIELD9")
    private String field9;

    @Schema(description = "数据表字段")
    @TableField("FIELD10")
    private String field10;

    @Schema(description = "数据表字段")
    @TableField("FIELD11")
    private String field11;

    @Schema(description = "数据表字段")
    @TableField("FIELD12")
    private String field12;

    @Schema(description = "数据表字段")
    @TableField("FIELD13")
    private String field13;

    @Schema(description = "数据表字段")
    @TableField("FIELD14")
    private String field14;

    @Schema(description = "数据表字段")
    @TableField("FIELD15")
    private String field15;

    @Schema(description = "数据表字段")
    @TableField("FIELD16")
    private String field16;

    @Schema(description = "数据表字段")
    @TableField("FIELD17")
    private String field17;

    @Schema(description = "数据表字段")
    @TableField("FIELD18")
    private String field18;

    @Schema(description = "数据表字段")
    @TableField("FIELD19")
    private String field19;

    @Schema(description = "数据表字段")
    @TableField("FIELD20")
    private String field20;

    @Schema(description = "数据表字段")
    @TableField("FIELD21")
    private String field21;

    @Schema(description = "数据表字段")
    @TableField("FIELD22")
    private String field22;

    @Schema(description = "数据表字段")
    @TableField("FIELD23")
    private String field23;

    @Schema(description = "数据表字段")
    @TableField("FIELD24")
    private String field24;

    @Schema(description = "数据表字段")
    @TableField("FIELD25")
    private String field25;

    @Schema(description = "数据表字段")
    @TableField("FIELD26")
    private String field26;

    @Schema(description = "数据表字段")
    @TableField("FIELD27")
    private String field27;

    @Schema(description = "数据表字段")
    @TableField("FIELD28")
    private String field28;

    @Schema(description = "数据表字段")
    @TableField("FIELD29")
    private String field29;

    @Schema(description = "数据表字段")
    @TableField("FIELD30")
    private String field30;

    @Schema(description = "数据表字段")
    @TableField("FIELD31")
    private String field31;

    @Schema(description = "数据表字段")
    @TableField("FIELD32")
    private String field32;

    @Schema(description = "数据表字段")
    @TableField("FIELD33")
    private String field33;

    @Schema(description = "数据表字段")
    @TableField("FIELD34")
    private String field34;

    @Schema(description = "数据表字段")
    @TableField("FIELD35")
    private String field35;

    @Schema(description = "数据表字段")
    @TableField("FIELD36")
    private String field36;

    @Schema(description = "数据表字段")
    @TableField("FIELD37")
    private String field37;

    @Schema(description = "数据表字段")
    @TableField("FIELD38")
    private String field38;

    @Schema(description = "数据表字段")
    @TableField("FIELD39")
    private String field39;

    @Schema(description = "数据表字段")
    @TableField("FIELD40")
    private String field40;

    @Schema(description = "数据表字段")
    @TableField("FIELD41")
    private String field41;

    @Schema(description = "数据表字段")
    @TableField("FIELD42")
    private String field42;

    @Schema(description = "数据表字段")
    @TableField("FIELD43")
    private String field43;

    @Schema(description = "数据表字段")
    @TableField("FIELD44")
    private String field44;

    @Schema(description = "数据表字段")
    @TableField("FIELD45")
    private String field45;

    @Schema(description = "数据表字段")
    @TableField("FIELD46")
    private String field46;

    @Schema(description = "数据表字段")
    @TableField("FIELD47")
    private String field47;

    @Schema(description = "数据表字段")
    @TableField("FIELD48")
    private String field48;

    @Schema(description = "数据表字段")
    @TableField("FIELD49")
    private String field49;

    @Schema(description = "数据表字段")
    @TableField("FIELD50")
    private String field50;

}
