package com.qm.ep.rebatecalc.mapper;

import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExecFormalCalcHistoryMapper extends QmBaseMapper<ExecFormalCalcHistoryDO> {

    /**
     * 获取计算对象的计算历史
     * @param calcObjectId 计算对象ID
     * @return 返回
     */
    List<ExecFormalCalcHistoryDO> getExecCalcHistory(String calcObjectId);

    void updateExecutorThreadName(@Param("executorThreadName") String executorThreadName, @Param("id") String id);
}
