package com.qm.ep.rebatecalc.ds.decorator;

import com.qm.ep.rebatecalc.ds.constant.Constants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.TaskDecorator;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 线程上下文拷贝 TaskDecorator
 * <AUTHOR>
 */
public class ContextDecorator implements TaskDecorator {

    @Override
    public Runnable decorate(Runnable runnable) {
        ServletRequestAttributes context = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        String tenantId = null;
        String companyId = null;
        if (null != context) {
            HttpServletRequest request = context.getRequest();
            tenantId = request.getParameter(Constants.TENANT_ID);
            companyId = request.getParameter(Constants.COMPANY_ID);
            if (StringUtils.isBlank(tenantId)) {
                tenantId = request.getHeader(Constants.TENANT_ID);
            }
            if (StringUtils.isBlank(companyId)) {
                companyId = request.getHeader(Constants.COMPANY_ID);
            }
        }
        if(null == tenantId) {
            tenantId = HeaderContextHolder.getHeader(Constants.TENANT_ID);
        }
        if(null == companyId) {
            companyId = HeaderContextHolder.getHeader(Constants.COMPANY_ID);
        }
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("tenantId", tenantId);
        headerMap.put("companyId", companyId);


        return () -> {
            try {
                RequestContextHolder.setRequestAttributes(context);
                HeaderContextHolder.setContext(headerMap);
                runnable.run();
            } finally {
                RequestContextHolder.resetRequestAttributes();
                HeaderContextHolder.removeContext();
            }
        };
    }
}