package com.qm.ep.rebatecalc.monitor.threadpool.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class DateUtils {
    private DateUtils() {
        throw new IllegalStateException("Utility class");
    }
    public static String getTodayStr() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(new Date());
    }

    public static String getCurrentTime() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return dateFormat.format(new Date());
    }

    public static String getCurrentMin() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm");
        return dateFormat.format(new Date());
    }

    public static String getCurrentTimeStamp() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SS");
        return dateFormat.format(new Date());
    }
}
