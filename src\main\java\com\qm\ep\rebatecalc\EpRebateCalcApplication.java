package com.qm.ep.rebatecalc;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 */
@EnableHystrix
@SpringBootApplication(scanBasePackages = "com.qm")
@EnableFeignClients(basePackages = {"com.qm"})
@EnableDiscoveryClient
@EnableTransactionManagement
@MapperScan({"com.qm.tds.dynamic.mapper", "com.qm.tds.base.mapper", "com.qm.ep.rebatecalc.mapper"})
public class EpRebateCalcApplication {

    public static void main(String[] args) {
        SpringApplication.run(EpRebateCalcApplication.class, args);
    }
}
