# 动态数据源（经销商多租户）要成功切换，需在此包里写相应的controller方法

启明的动态数据源方案，适用于多销售公司模式，但是按照不同的经销商再拆分租户时，不能很好支持。

为了实现经销商端的多租户方案，需要做以下基本操作
1、脱离启明多租户的框架，将数据源配置中的primary设置为master（非tanent）
原因：
com.qm.tds.dynamic.config.DynamicDataSourceInterceptor类里，有这样的代码

```java
public class DynamicDataSourceInterceptor extends DynamicDataSourceAnnotationInterceptor {
    private static final Logger log = LoggerFactory.getLogger(DynamicDataSourceInterceptor.class);
    private static final String DYNAMIC_PREFIX = "#";
    private final DataSourceClassResolver dataSourceClassResolver;
    private final DsProcessor dsProcessor;
    @Value("${spring.datasource.dynamic.primary}")
    private String primary;
    @Autowired(
            required = false
    )
    private DataSourceSupporter supporter;

    public DynamicDataSourceInterceptor(Boolean allowedPublicOnly, DsProcessor dsProcessor) {
        super(allowedPublicOnly, dsProcessor);
        this.dataSourceClassResolver = new DataSourceClassResolver(allowedPublicOnly);
        this.dsProcessor = dsProcessor;
    }

    public Object invoke(MethodInvocation invocation) throws Throwable {
        Object var2;
        try {
            DynamicDataSourceContextHolder.push(this.determineDatasource(invocation));
            var2 = invocation.proceed();
        } finally {
            DynamicDataSourceContextHolder.poll();
        }

        return var2;
    }

    private String determineDatasource(MethodInvocation invocation) throws Throwable {
        Method method = invocation.getMethod();
        String groupKey;
        if ("tenant".equals(this.primary)) {
            String tenantId = this.getTenantId();
            if (StringUtils.isNotBlank(tenantId)) {
                this.supporter.dataSourceHandle(tenantId);
                DS ds = (DS)method.getAnnotation(DS.class);
                if (ds != null) {
                    if (!ds.value().equals("tenant") && !ds.value().equals("master") && !ds.value().equals("slave")) {
                        groupKey = tenantId + ds.value();
                    } else {
                        groupKey = this.determineDatasourceKey(invocation);
                    }
                } else {
                    groupKey = tenantId + "w";
                }
            } else {
                groupKey = this.determineDatasourceKey(invocation);
            }
        } else {
            groupKey = this.determineDatasourceKey(invocation);
        }

        return groupKey;
    }

    private String determineDatasourceKey(MethodInvocation invocation) {
        String key = this.dataSourceClassResolver.findDSKey(invocation.getMethod(), invocation.getThis());
        return !key.isEmpty() && key.startsWith("#") ? this.dsProcessor.determineDatasource(invocation, key) : key;
    }

    private String getTenantId() {
        String tenantId = null;
        ServletRequestAttributes attributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
        if (null != attributes) {
            HttpServletRequest request = attributes.getRequest();
            tenantId = request.getParameter("tenantId");
            if (StringUtils.isBlank(tenantId)) {
                tenantId = request.getHeader("tenantId");
            }
        }

        return tenantId;
    }
}
```
这段代码已经强耦合了启明的动态数据源逻辑，而且破坏了dynamic-dataSource非常好的spel机制（@DS("#dsname")）
使用spel时，会将tenantId和数据源名称拼接，造成无法切换数据源，例如拼接后，变成：15#dsname

2、将controller写到dscontroller包下
原因：
dynamic-dataSource在切换数据源时，如果外层被@Transactional包裹了，那么内部的方法，再使用@DS进行切源，是不会成功的
在一个事务下，你切换数据源，怎么可能会自动保证事务呢，所以，索性dynamic-dataSource就不切换数据源了

由于以下代码，导致controller被aop默认包裹在一个事务中了，所以无法进行切换，故要换一个不被增强的包
com.qm.tds.api.config.TransactionAdviceConfig
```java
@Aspect
@Configuration
public class TransactionAdviceConfig {
    @Autowired
    private PlatformTransactionManager transactionManager;
    private static final String AOP_POINTCUT_EXPRESSION_CONTROLLER = "execution(* com.qm.ep.*.controller..*(..))|| execution(* com.qm.tds.*.controller..*(..))";

    public TransactionAdviceConfig() {
    }

    @Bean
    public TransactionInterceptor txAdviceController() {
        DefaultTransactionAttribute txAttrRequired = new DefaultTransactionAttribute();
        txAttrRequired.setPropagationBehavior(3);
        NameMatchTransactionAttributeSource source = new NameMatchTransactionAttributeSource();
        source.addTransactionalMethod("*", txAttrRequired);
        return new TransactionInterceptor(this.transactionManager, source);
    }

    @Bean
    public Advisor txAdviceAdvisorController() {
        AspectJExpressionPointcut pointcut = new AspectJExpressionPointcut();
        pointcut.setExpression("execution(* com.qm.ep.*.controller..*(..))|| execution(* com.qm.tds.*.controller..*(..))");
        return new DefaultPointcutAdvisor(pointcut, this.txAdviceController());
    }
}
```

启动一个新的线程去执行一个切源的方法，也是可行的，因为新的线程不被事务包裹，但是前提也得是把primary设置为master（非tanent）