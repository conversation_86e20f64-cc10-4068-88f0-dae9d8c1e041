package com.qm.ep.rebatecalc.constant;

/**
 * 常量类
 * <AUTHOR>
 */
public class MqConstants {
    private MqConstants() {
    }

    public static final String MIRROR_FORMAL_CALC_RESULT_EXCHANGE = "qm.rebatecalc.mirror.formal.calc.result.exchange";
    public static final String MIRROR_FORMAL_CALC_RESULT_QUEUE = "qm-rebatecalc-mirror-formal-calc-result-queue";
    public static final String MIRROR_FORMAL_CALC_RESULT_ROUTING_KEY = "mirror_result";

}
