package com.qm.ep.rebatecalc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcResultDO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcHistoryDTO;
import com.qm.ep.rebatecalc.domain.dto.TurnBusinessBottomTableDTO;
import com.qm.ep.rebatecalc.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebatecalc.enumerate.StateEnum;
import com.qm.ep.rebatecalc.remote.RebateBaseRemote;
import com.qm.ep.rebatecalc.service.ExecFormalCalcHistoryService;
import com.qm.ep.rebatecalc.service.ExecFormalCalcResultService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/execFormalCalcHistory")
@Tag(description = "[author:50013723]", name = "计算对象计算历史")
@Slf4j
public class ExecFormalCalcHistoryController extends BaseController {

    private static final String LEAVE_VERSION_COUNT = "leaveVersionCount";
    private static final String PER_REQ_DEAL_CALC_HISTORY_COUNT = "perReqDealCalcHistoryCount";

    @Resource
    private ExecFormalCalcHistoryService execCalcHistoryService;

    @Resource
    private ExecFormalCalcResultService execCalcResultService;

    @Autowired
    private RebateBaseRemote rebateBaseRemote;


    @Operation(summary = "查询计算对象计算历史列表", description= "[author:50013723]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ExecFormalCalcHistoryDO>> tableList(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        JsonResultVo<QmPage<ExecFormalCalcHistoryDO>> ret = new JsonResultVo<>();
        ret.setData(execCalcHistoryService.tableList(execCalcHistoryDTO));
        return ret;
    }

    @Operation(summary = "查询计算对象计算字段列表", description= "[author:50013723]")
    @PostMapping("/fields")
    public JsonResultVo<List<String>> getFields(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        JsonResultVo<List<String>> ret = new JsonResultVo<>();
        ret.setData(execCalcHistoryService.getFields(execCalcHistoryDTO));
        return ret;
    }

    @Operation(summary = "删除计算对象计算历史记录", description= "[author:50013723]")
    @PostMapping("/deleteList")
    public JsonResultVo<String> deleteList(@RequestBody List<String> deleteIds) {
        JsonResultVo<String> ret = new JsonResultVo<>();
        execCalcHistoryService.deleteList(deleteIds);
        ret.setMsg("删除成功");
        return ret;
    }

    @Operation(summary = "通过计算对象ID，删除计算因子计算历史记录", description= "[author:50013723]")
    @PostMapping("/deleteByCalcObjectIds")
    public JsonResultVo<String> deleteByCalcObjectIds(@RequestBody List<String> calcObjectIds) {
        if(calcObjectIds.isEmpty()) {
            throw new QmException("未选择删除记录！");
        }
        QmQueryWrapper<ExecFormalCalcHistoryDO> queryHistoryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcHistoryDO> lambdaHistoryWrapper = queryHistoryWrapper.lambda();
        lambdaHistoryWrapper.in(ExecFormalCalcHistoryDO::getObjectId, calcObjectIds);
        execCalcHistoryService.remove(lambdaHistoryWrapper);

        lambdaHistoryWrapper.select(ExecFormalCalcHistoryDO.class, fieldInfo->"ID".equalsIgnoreCase(fieldInfo.getColumn()));
        List<String> historyIds = execCalcHistoryService.list(lambdaHistoryWrapper).stream().map(ExecFormalCalcHistoryDO::getId).collect(Collectors.toList());

        if(!historyIds.isEmpty()) {
            QmQueryWrapper<ExecFormalCalcResultDO> queryResultWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<ExecFormalCalcResultDO> lambdaResultWrapper = queryResultWrapper.lambda();
            lambdaResultWrapper.in(ExecFormalCalcResultDO::getHistoryId, historyIds);
            execCalcResultService.remove(lambdaResultWrapper);
        }
        JsonResultVo<String> ret = new JsonResultVo<>();
        ret.setMsg("删除成功");
        return ret;
    }
    @Operation(summary = "通过政策id，删除计算因子计算历史记录", description= "[author:50013723]")
    @PostMapping("/deleteByHistoryPolicyId")
    public boolean deleteByHistoryPolicyId(@RequestBody String policyId) {
        QmQueryWrapper<ExecFormalCalcHistoryDO> historyWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcHistoryDO> historylambdaWrapper = historyWrapper.lambda();
        historylambdaWrapper.eq(ExecFormalCalcHistoryDO::getPolicyId, policyId);
        return execCalcHistoryService.remove(historylambdaWrapper);
    }

    @Operation(summary = "按照版本，清除过期的阶梯数据。对于正式计算，只有阶梯需要做清理，其他的不需要", description= "[author:50013723]")
    @PostMapping("/deleteExpiredLadderHistoriesByVersion")
    public JsonResultVo<String> deleteExpiredLadderHistoriesByVersion(@RequestBody Map<String, Long> params) {
        JsonResultVo<String> ret = new JsonResultVo<>();
        int leaveVersionCount = 3;
        int perReqDealCalcHistoryCount = 100;
        if(params.containsKey(LEAVE_VERSION_COUNT)) {
            try {
                leaveVersionCount = Integer.parseInt(String.valueOf(params.get(LEAVE_VERSION_COUNT)));
            } catch (Exception e) {
                log.error("获取参数{}出错！无法解析！", LEAVE_VERSION_COUNT);
            }
        }
        if(params.containsKey(PER_REQ_DEAL_CALC_HISTORY_COUNT)) {
            try {
                perReqDealCalcHistoryCount = Integer.parseInt(String.valueOf(params.get(PER_REQ_DEAL_CALC_HISTORY_COUNT)));
            } catch (Exception e) {
                log.error("获取参数{}出错！无法解析！", PER_REQ_DEAL_CALC_HISTORY_COUNT);
            }
        }
        QmQueryWrapper<ExecFormalCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(ExecFormalCalcHistoryDO::getState, StateEnum.FINISH).eq(ExecFormalCalcHistoryDO::getObjectType, CalcObjectTypeEnum.LADDER).groupBy(ExecFormalCalcHistoryDO::getObjectId).having("count(object_id)>{0}", leaveVersionCount).last("limit "+perReqDealCalcHistoryCount);
        List<String> objectIds = execCalcHistoryService.list(lambdaWrapper).stream().map(ExecFormalCalcHistoryDO::getObjectId).collect(Collectors.toList());
        int finalLeaveVersionCount = leaveVersionCount;
        objectIds.forEach(i->{
            QmQueryWrapper<ExecFormalCalcHistoryDO> queryHistoryWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<ExecFormalCalcHistoryDO> lambdaHistoryWrapper = queryHistoryWrapper.lambda();
            lambdaHistoryWrapper.eq(ExecFormalCalcHistoryDO::getObjectId, i)
                    .eq(ExecFormalCalcHistoryDO::getState, StateEnum.FINISH)
                    .eq(ExecFormalCalcHistoryDO::getObjectType, CalcObjectTypeEnum.LADDER)
                    .orderByDesc(ExecFormalCalcHistoryDO::getCalcVersion).last("limit "+ finalLeaveVersionCount +",1000000");
            List<String> historyIds = execCalcHistoryService.list(lambdaHistoryWrapper).stream().map(ExecFormalCalcHistoryDO::getId).collect(Collectors.toList());
            QmQueryWrapper<ExecFormalCalcResultDO> queryResultWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<ExecFormalCalcResultDO> lambdaResultWrapper = queryResultWrapper.lambda();
            lambdaResultWrapper.in(ExecFormalCalcResultDO::getHistoryId, historyIds);
            execCalcResultService.remove(lambdaResultWrapper);
            execCalcHistoryService.removeByIds(historyIds);
        });
        ret.setMsg("清除过期阶梯数据成功！");
        return ret;
    }

    @Operation(summary = "计算结果转业务底表", description= "[author:50013723]")
    @PostMapping("/formalTurnBusinessBottomTable")
    public JsonResultVo<String> formalTurnBusinessBottomTable(@RequestBody TurnBusinessBottomTableDTO turnBusinessBottomTableDTO) {
        ExecFormalCalcHistoryDO execFormalCalcHistoryDO = execCalcHistoryService.getById(turnBusinessBottomTableDTO.getId());
        execFormalCalcHistoryDO.setState(StateEnum.CONVERTING);
        execCalcHistoryService.updateById(execFormalCalcHistoryDO);
        turnBusinessBottomTableDTO.setStructure(execFormalCalcHistoryDO.getSqlStructure());
        JsonResultVo<String> reportSqlStructure = rebateBaseRemote.turnBusinessBottomTable(turnBusinessBottomTableDTO);
        ExecFormalCalcHistoryDO execFormalCalcHistory = execCalcHistoryService.getById(turnBusinessBottomTableDTO.getId());
        execFormalCalcHistory.setState(StateEnum.FINISH);
        execCalcHistoryService.updateById(execFormalCalcHistory);
        return reportSqlStructure;
    }

    @Operation(summary = "获取政策计算历史列表", description= "[author:50013723]")
    @PostMapping("/getExecHistoryList")
    public JsonResultVo<List<ExecFormalCalcHistoryDO>> getExecHistoryList(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        JsonResultVo<List<ExecFormalCalcHistoryDO>> ret = new JsonResultVo<>();
        QmQueryWrapper<ExecFormalCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(ExecFormalCalcHistoryDO::getPolicyId, execCalcHistoryDTO.getPolicyId())
                .eq(ExecFormalCalcHistoryDO::getObjectId, execCalcHistoryDTO.getObjectId())
                .eq(ExecFormalCalcHistoryDO::getObjectType, execCalcHistoryDTO.getObjectType());

        List<ExecFormalCalcHistoryDO> data = execCalcHistoryService.list(queryWrapper);
        ret.setData(data);
        return ret;
    }

    @Operation(summary = "获取政策计算历史", description= "[author:50013723]")
    @PostMapping("/getExecHistory")
    public JsonResultVo<ExecFormalCalcHistoryDO> getExecHistory(@RequestBody ExecCalcHistoryDTO execCalcHistoryDTO) {
        JsonResultVo<ExecFormalCalcHistoryDO> ret = new JsonResultVo<>();
        ExecFormalCalcHistoryDO data = execCalcHistoryService.getById(execCalcHistoryDTO.getId());
        ret.setData(data);
        return ret;
    }
}
