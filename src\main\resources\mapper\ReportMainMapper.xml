<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.rebatecalc.mapper.ReportMainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.rebatecalc.domain.bean.ReportMainDO">
        <id column="ID" property="id"/>
        <result column="REPORT_NAME" property="reportName"/>
        <result column="CASH_OBJECT" property="cashObject"/>
        <result column="DESCRIPTION" property="description"/>
        <result column="CALCULATION_STATUS" property="calculationStatus"/>
        <result column="BEGIN_TIME" property="beginTime"/>
        <result column="END_TIME" property="endTime"/>
        <result column="CREATE_BY" property="createBy"/>
        <result column="CREATE_ON" property="createOn"/>
        <result column="UPDATE_BY" property="updateBy"/>
        <result column="UPDATE_ON" property="updateOn"/>
        <result column="CREATE_BY_NAME" property="createByName"/>
        <result column="UPDATE_BY_NAME" property="updateByName"/>
        <result column="DTSTAMP" property="dtstamp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    ID, REPORT_NAME, CASH_OBJECT, DESCRIPTION, CALCULATION_STATUS,
    BEGIN_TIME, END_TIME, CREATE_BY, CREATE_ON, UPDATE_BY, UPDATE_ON, DTSTAMP
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select * from (
            select
                rm.ID,
                rm.REPORT_NAME,
                rm.CASH_OBJECT,
                rm.DESCRIPTION,
                rm.CALCULATION_STATUS,
                rm.BEGIN_TIME,
                rm.END_TIME,
                rm.CREATE_BY,
                rm.CREATE_ON,
                rm.UPDATE_BY,
                rm.UPDATE_ON,
                rm.DTSTAMP,
                b.vtext as CREATE_BY_NAME,
                c.vtext as UPDATE_BY_NAME
            from report_main rm
            left join SYSC000_M b on rm.CREATE_BY = b.NMAINID
            left join SYSC000_M c on rm.UPDATE_BY = c.NMAINID
        ) innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultMap="BaseResultMap">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultMap="BaseResultMap">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultMap="BaseResultMap">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultMap="BaseResultMap">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultMap="BaseResultMap">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultMap="BaseResultMap">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultMap="BaseResultMap">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>

</mapper>
