package com.qm.ep.rebatecalc.service;

import com.qm.ep.rebatecalc.domain.dto.ExecCalcDTO;
import com.qm.ep.rebatecalc.enumerate.CalcTypeEnum;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.scheduling.annotation.Async;

/**
 * <AUTHOR>
 */
public interface ExecCalcService {

    /**
     * 试算准备
     * @param execCalcDTO 参数
     * @return 返回
     */
    String prepareCalc(ExecCalcDTO execCalcDTO);

    /**
     * 发起计算对象试算
     * @param calcType 计算类型
     * @param historyId 试算历史ID
     * @throws InterruptedException 异常
     */
    void execCalc(CalcTypeEnum calcType, String historyId,String lockKey) throws InterruptedException;

    /**
     * 计算的包裹，统一
     * @param execCalcDTO 参数
     * @return 返回
     */
    JsonResultVo<String> startCalc(ExecCalcDTO execCalcDTO);

    /**
     * 计算所有符合条件的政策的阶梯
     */
    @Async("execCalcAsync")
    void calcLadder();
}
