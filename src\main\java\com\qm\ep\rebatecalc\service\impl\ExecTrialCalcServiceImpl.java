package com.qm.ep.rebatecalc.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatecalc.constant.Constants;
import com.qm.ep.rebatecalc.domain.bean.ExecTrialCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.dto.CalcObjectDTO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcDTO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcLogStructureDTO;
import com.qm.ep.rebatecalc.domain.vo.SqlStructureVO;
import com.qm.ep.rebatecalc.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebatecalc.enumerate.ExecTypeEnum;
import com.qm.ep.rebatecalc.enumerate.StateEnum;
import com.qm.ep.rebatecalc.mapper.ExecTrialCalcHistoryMapper;
import com.qm.ep.rebatecalc.monitor.threadpool.report.redis.RedisService;
import com.qm.ep.rebatecalc.remote.RebateBaseRemote;
import com.qm.ep.rebatecalc.service.ExecCalcLogService;
import com.qm.ep.rebatecalc.service.ExecTrialCalcHistoryService;
import com.qm.ep.rebatecalc.service.ExecTrialCalcResultService;
import com.qm.ep.rebatecalc.service.ExecTrialCalcService;
import com.qm.ep.rebatecalc.utils.SqlUtils;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExecTrialCalcServiceImpl implements ExecTrialCalcService {

    @Autowired
    private RedisService redisService;
    private static final int MAX_RETRY_TIME = 50;

    @Resource
    private ExecCalcLogService execCalcLogService;

    @Resource
    RebateBaseRemote rebateBaseRemote;

    @Resource
    ExecTrialCalcHistoryService execTrialCalcHistoryService;

    @Resource
    ExecTrialCalcResultService execTrialCalcResultService;

    @Resource
    ExecTrialCalcHistoryMapper execTrialCalcHistoryMapper;

    @Value("${rebate.exec-calc.result.max-amount:6000000}")
    private Long execCalcResultRecordMaxAmount;

    /**
     * 试算准备
     *
     * @param execCalcDTO 参数
     * @return 返回
     */
    @Override
    // @Transactional(propagation= Propagation.REQUIRES_NEW)
    public String prepareCalc(ExecCalcDTO execCalcDTO) {
        log.info("准备计算执行：prepareCalc(ExecCalcDTO execCalcDTO={})开始",execCalcDTO);
        /*if(BootAppUtil.isNullOrEmpty(execCalcDTO.getPolicyId()) || BootAppUtil.isNullOrEmpty(execCalcDTO.getObjectId()) || BootAppUtil.isNullOrEmpty(execCalcDTO.getObjectType())) {
            throw new QmException("政策ID或计算对象ID或计算对象类型为空！");
        }*/
        CalcObjectDTO calcObjectDTO = new CalcObjectDTO();
        calcObjectDTO.setPolicyId(execCalcDTO.getPolicyId());
        calcObjectDTO.setObjectId(execCalcDTO.getObjectId());
        calcObjectDTO.setObjectType(execCalcDTO.getObjectType());
        calcObjectDTO.setAbbreviated(true);
        JsonResultVo<SqlStructureVO> sqlStructure = rebateBaseRemote.getSqlStructure(calcObjectDTO);

        LambdaQueryWrapper<ExecTrialCalcHistoryDO> execCalcHistoryWrapper = new QmQueryWrapper<ExecTrialCalcHistoryDO>().lambda();
        execCalcHistoryWrapper.eq(BootAppUtil.isnotNullOrEmpty(execCalcDTO.getPolicyId()), ExecTrialCalcHistoryDO::getPolicyId, execCalcDTO.getPolicyId())
                .eq(ExecTrialCalcHistoryDO::getObjectId, execCalcDTO.getObjectId())
                .eq(ExecTrialCalcHistoryDO::getObjectType, execCalcDTO.getObjectType())
                .orderByDesc(ExecTrialCalcHistoryDO::getCalcVersion).last("limit 1");
        ExecTrialCalcHistoryDO maxExecHistory = execTrialCalcHistoryService.getOne(execCalcHistoryWrapper);

        ExecTrialCalcHistoryDO execCalcHistoryDO = new ExecTrialCalcHistoryDO();
        execCalcHistoryDO.setPolicyId(execCalcDTO.getPolicyId());
        execCalcHistoryDO.setObjectId(execCalcDTO.getObjectId());
        execCalcHistoryDO.setObjectType(execCalcDTO.getObjectType());
        execCalcHistoryDO.setExecType(null==execCalcDTO.getExecType()?ExecTypeEnum.MANUAL:execCalcDTO.getExecType());
        execCalcHistoryDO.setCalcVersion(maxExecHistory==null ? 1 : maxExecHistory.getCalcVersion() + 1);
        execCalcHistoryDO.setState(StateEnum.PROCESS);
        execCalcHistoryDO.setBegin(DateUtils.getSysdateTime());
        execCalcHistoryDO.setSqlStructure(JSONUtils.beanToJson(sqlStructure.getData()));

        List<ExecCalcLogStructureDTO> execCalcLogStructures = new ArrayList<>();
        execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("创建试算任务").build());
        execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行SQL：" + sqlStructure.getData().getTemporaryTableSqlFormatted() + "\n\n" + sqlStructure.getData().getSqlFormatted()).build());

        execCalcHistoryDO.setLog(execCalcLogService.append(null, execCalcLogStructures));
        execCalcHistoryDO.setDtstamp(DateUtils.getSysdateTime());
        execTrialCalcHistoryService.save(execCalcHistoryDO);
        log.info("准备计算执行：prepareCalc(ExecCalcDTO execCalcDTO={})结束",execCalcDTO);
        return execCalcHistoryDO.getId();
    }

    /**
     * 发起计算对象试算
     *
     * @param historyId 参数
     */
    @Override
    @DS(DataSourceType.W)
    // @Transactional(rollbackFor = Exception.class)
    public void execCalc(String historyId,String lockKey) throws InterruptedException {
        SqlStructureVO sqlStructure = null;
        Map<String, Object> sqlMap = new HashMap<>(1);
        log.info("试算执行：execCalc(String historyId={}) 开始",historyId);
        int retryCount = 0;
        ExecTrialCalcHistoryDO execCalcHistoryDO = null;
        while (retryCount < MAX_RETRY_TIME) {
            execCalcHistoryDO = execTrialCalcHistoryService.getById(historyId);
            if(execCalcHistoryDO!=null) {
                break;
            }
            retryCount++;
            TimeUnit.SECONDS.sleep(3);
        }
        List<ExecCalcLogStructureDTO> execCalcLogStructures = new ArrayList<>();
        if(execCalcHistoryDO==null) {
            log.error("获取当前试算历史{}日志出错，本次试算退出", historyId);
            return;
        } else {
            log.info("获取当前试算历史{}日志成功！尝试次数：{}", historyId, retryCount);
        }

        execCalcHistoryDO.setExecutorThreadName(Thread.currentThread().getName());
        //execTrialCalcHistoryService.updateById(execCalcHistoryDO);
        //execTrialCalcHistoryMapper.updateExecutorThreadName(execCalcHistoryDO.getExecutorThreadName(),historyId);

        //这里为啥有取一次数据
        //execCalcHistoryDO = execTrialCalcHistoryService.getById(historyId);



        try {
            String sqlStructureBase = execCalcHistoryDO.getSqlStructure();
             sqlStructure = JSONUtils.packingDOFromJsonStr(sqlStructureBase, SqlStructureVO.class);


            if(BootAppUtil.isnotNullOrEmpty(sqlStructure.getTemporaryTableSqlFormatted())) {
                sqlMap.put("sql", sqlStructure.getTemporaryTableSqlFormatted());
                execTrialCalcResultService.getBySql(sqlMap);
            }

            String sqlbase = "(" + sqlStructure.getSql() + ") as sqlbase";
            sqlMap.put("sql", "select count(*) as total from " + sqlbase);
            // 创建临时表
            log.info("创建临时表：execTrialCalcResultService.getBySql开始");

            List<Map> countResult = execTrialCalcResultService.getBySql(sqlMap);

            log.info("创建临时表：execTrialCalcResultService.getBySql结束");
            long total = Long.parseLong(String.valueOf(countResult.get(0).get("total")));
            JsonResultVo<String> sysConfigResult = rebateBaseRemote.getValueByCode(Constants.RESULT_MAX_AMOUNT);
            long maxAmount = sysConfigResult.isOk() ? Long.valueOf(sysConfigResult.getData()) : execCalcResultRecordMaxAmount;
            if(CollUtil.isNotEmpty(countResult) && total<=maxAmount) {
                List<String> selectFields = sqlStructure.getFields();
                int fieldSize = selectFields.size();
                List<String> fields = new ArrayList<>();
                List<String> sumFields = new ArrayList<>();
                for (int i = 1; i <= fieldSize; i++) {
                    fields.add("field" + i);
                    sumFields.add("sum(" + SqlUtils.emphasis(SqlUtils.aliasName(true, selectFields.get(i-1))) + ") as field" + i);
                }

                String sql = " insert into exec_trial_calc_result(historyId,"+ StringUtils.join(fields, ",") +") \n" +
                        " select '" + historyId + "' as historyId,sqlbase.* from " + sqlbase;

                sqlMap.put("sql", sql);

                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算SQL语句").build());

                log.info("保存试算结果：execTrialCalcResultService.saveBySql开始");

                int insertCount = execTrialCalcResultService.saveBySql(sqlMap);

                log.info("保存试算结果：execTrialCalcResultService.saveBySql结束");

                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算成功，共生成"+insertCount+"条记录").build());

                // 对计算结果 求sum
                String sumSql = " select " + StringUtils.join(sumFields, ",") + " from " + sqlbase;
                sqlMap.put("sql", sumSql);
                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL语句：" + sumSql).build());


                List<Map> sumResult = execTrialCalcResultService.getBySql(sqlMap);


                if(CollUtil.isNotEmpty(sumResult)) {
                    String rs = JSONUtils.beanToJson(sumResult.get(0));
                    execCalcHistoryDO.setSumResult(rs);
                    execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 成功！结果为：" + rs).build());
                } else {
                    execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行求和SQL 失败！无结果").status(Constants.ERROR_STATUS).build());
                }

                execCalcHistoryDO.setState(StateEnum.FINISH);
            } else {
                execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("试算结果记录数"+total+"。由于记录数大于500万，本次试算终止！请检查配置是否正确！").status(Constants.ERROR_STATUS).build());
                execCalcHistoryDO.setState(StateEnum.ERROR);
            }


        } catch (Exception e) {
            execCalcHistoryDO.setState(StateEnum.ERROR);
            execCalcLogStructures.add(ExecCalcLogStructureDTO.builder().content("执行试算出错\n"+e.getMessage()).status(Constants.ERROR_STATUS).build());
        } finally {
            redisService.unlock(lockKey);
            log.info("试算执行：execCalc(String historyId={}) 结束前",historyId);

            // 删除临时表
            log.info("删除临时表：execTrialCalcResultService.getBySql开始");

            Map<CalcObjectTypeEnum, Map<String, String>> temporaryTableSqlMap = sqlStructure.getTemporaryTableSql();
            if(null!=temporaryTableSqlMap) {
                List<String> dropTemporaryTableSql = new ArrayList<>();
                temporaryTableSqlMap.values().forEach(s-> s.keySet().forEach(t-> dropTemporaryTableSql.add(" DROP TABLE IF EXISTS " + SqlUtils.emphasis(t) + " ; ")));
                if(CollUtil.isNotEmpty(dropTemporaryTableSql)) {
                    sqlMap.put("sql", StringUtils.join(dropTemporaryTableSql, "\n"));
                    try {
                        execTrialCalcResultService.getBySql(sqlMap);
                    }catch (Exception e) {
                        log.error("Failed to delete temporary table",e);
                    }

                }
            }

            log.info("删除临时表：execTrialCalcResultService.getBySql结束");

            execCalcHistoryDO.setLog(execCalcLogService.append(execCalcHistoryDO.getLog(), execCalcLogStructures));
            execCalcHistoryDO.setEnd(DateUtils.getSysdateTime());

            //不更新SqlStructure
            execCalcHistoryDO.setSqlStructure(null);

            //这里经常出现链接断开：Error updating database.  Cause: java.sql.SQLException: connection disabled
            execTrialCalcHistoryService.updateById(execCalcHistoryDO);
            log.info("试算执行：execCalc(String historyId={}) 结束后",historyId);


        }
    }
}
