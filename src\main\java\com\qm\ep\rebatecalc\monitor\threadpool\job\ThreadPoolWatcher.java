package com.qm.ep.rebatecalc.monitor.threadpool.job;

import com.qm.ep.rebatecalc.monitor.threadpool.config.MonitorCache;
import com.qm.ep.rebatecalc.monitor.threadpool.executor.ExecutorPeekRecordHolder;
import com.qm.ep.rebatecalc.monitor.threadpool.executor.MonitoredThreadPoolTaskExecutor;
import com.qm.ep.rebatecalc.monitor.threadpool.report.Reporter;
import com.qm.ep.rebatecalc.monitor.threadpool.report.ThreadPoolDetailInfo;
import com.qm.ep.rebatecalc.monitor.threadpool.utils.DateUtils;
import com.qm.tds.api.util.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池后台监视者
 * <AUTHOR>
 */
@Slf4j
public class ThreadPoolWatcher implements Runnable {

    private final Reporter reporter;

    public ThreadPoolWatcher() {
        this.reporter = SpringContextHolder.getBean(Reporter.class);
    }

    @Override
    public void run() {
        while (true) {
            try {
                for (String poolName : MonitorCache.executorMap.keySet()) {
                    ExecutorPeekRecordHolder executorPeekRecordHolder = MonitorCache.peekRecordHolderMap.get(poolName);
                    Executor executor = MonitorCache.executorMap.get(poolName);
                    ThreadPoolDetailInfo threadPoolDetailInfo = new ThreadPoolDetailInfo();
                    threadPoolDetailInfo.setPort(MonitorCache.port);
                    threadPoolDetailInfo.setApplicationName(MonitorCache.applicationName);
                    threadPoolDetailInfo.setPoolName(poolName);
                    threadPoolDetailInfo.setIp(MonitorCache.ip);
                    threadPoolDetailInfo.setActivePoolSize(executorPeekRecordHolder.getActiveThreadCountPeek());
                    if(executor instanceof MonitoredThreadPoolTaskExecutor) {
                        threadPoolDetailInfo.setCompletedTaskCount(((MonitoredThreadPoolTaskExecutor)executor).getThreadPoolExecutor().getCompletedTaskCount());
                        threadPoolDetailInfo.setMaximumPoolSize(((MonitoredThreadPoolTaskExecutor)executor).getThreadPoolExecutor().getMaximumPoolSize());
                        threadPoolDetailInfo.setErrorTaskNum(((MonitoredThreadPoolTaskExecutor)executor).getErrorNum());
                    } else if(executor instanceof ThreadPoolTaskExecutor) {
                        threadPoolDetailInfo.setCompletedTaskCount(((ThreadPoolTaskExecutor)executor).getThreadPoolExecutor().getCompletedTaskCount());
                        threadPoolDetailInfo.setMaximumPoolSize(((ThreadPoolTaskExecutor)executor).getThreadPoolExecutor().getMaximumPoolSize());
                        threadPoolDetailInfo.setErrorTaskNum(0);
                    } else if(executor instanceof ThreadPoolExecutor) {
                        threadPoolDetailInfo.setCompletedTaskCount(((ThreadPoolExecutor)executor).getCompletedTaskCount());
                        threadPoolDetailInfo.setMaximumPoolSize(((ThreadPoolExecutor)executor).getMaximumPoolSize());
                        threadPoolDetailInfo.setErrorTaskNum(0);
                    }
                    threadPoolDetailInfo.setQueueSize(executorPeekRecordHolder.getTaskQueueLengthPeek());
                    threadPoolDetailInfo.setRecordTime(DateUtils.getCurrentTimeStamp());
                    reporter.doReportThreadPoolInfo(threadPoolDetailInfo);
                    executorPeekRecordHolder.clearActiveCountAndQueueSize();
                }
                TimeUnit.SECONDS.sleep(15);
            } catch (Exception e) {
                log.error("[ThreadPoolWatcher] error is ", e);
            }
        }
    }
}
