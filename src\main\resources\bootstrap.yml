server:
  port: 8349
spring:
  application:
    name: tds-service-rebate-calc
  profiles:
    active: nacos
  # 配置中心，环境变量在k8s中配置
  cloud:
    nacos:
      discovery:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        namespace: ${spring.cloud.nacos.config.namespace}
        fail-fast: true
      config:
        server-addr: ${spring.cloud.nacos.config.server-addr}
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        namespace: ${spring.cloud.nacos.config.namespace}
        group: TDS_EP_NACOS_GROUP
        #配置文件类型，目前只支持 properties 和 yaml 类型，默认为 properties
        file-extension: yaml
        shared-configs[0]:
          data-id: policy-config.yml # 配置文件名-Data Id
          group: TDS_EP_NACOS_GROUP   # 默认为DEFAULT_GROUP
          refresh: true
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true