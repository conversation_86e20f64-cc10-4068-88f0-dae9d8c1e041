package com.qm.ep.rebatecalc.monitor.threadpool.report;


/**
 * <AUTHOR>
 */
public interface Reporter {

    /**
     * 上报线程池数据
     */
    void doReportTask(String ip,Integer port,String applicationName,String tagId,ReportInfo reportInfo);

    /**
     * 上报线程池实时数据信息
     *
     */
    void doReportRealTime(String ip,Integer port,String applicationName,ThreadPoolRealTimeInfo realTimeInfo);

    /**
     * 上报告警邮件地址信息
     *
     */
    void doReportAlarmInfo(String applicationName,String alarmEmails);

    /**
     * 上报执行了多少个任务
     *
     */
    void doReportTaskTimes(Integer taskTimes, ThreadPoolDetailInfo param);

    /**
     * 上报这个应用的所有线程池属性
     *
     */
    void doReportTotalData(TotalDataInfo totalDataInfo);

    /**
     * 上报线程池的信息
     *
     */
    void doReportThreadPoolInfo(ThreadPoolDetailInfo threadPoolDetailInfo);

    /**
     * 记录任务异常发生时间
     *
     */
    void doReportErrorTaskTimes(int i, ThreadPoolDetailInfo threadPoolDetailInfo);

    /**
     * 记录写入标签的发生时间
     *
     */
    void doReportTagTimes(int i, ThreadPoolDetailInfo threadPoolDetailInfo);

}
