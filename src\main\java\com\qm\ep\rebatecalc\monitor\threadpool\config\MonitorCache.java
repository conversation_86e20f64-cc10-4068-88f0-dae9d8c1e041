package com.qm.ep.rebatecalc.monitor.threadpool.config;

import cn.hutool.core.net.NetUtil;
import com.qm.ep.rebatecalc.monitor.threadpool.executor.ExecutorPeekRecordHolder;
import com.qm.ep.rebatecalc.monitor.threadpool.report.ReportInfo;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
public class MonitorCache {

    private MonitorCache() {
        throw new IllegalStateException("MonitorCache class");
    }

    public static String ip = NetUtil.getLocalhostStr();
    public static Integer port;
    public static String applicationName;
    public static Map<String, Executor> executorMap = new HashMap<>();
    public static Map<String, AtomicInteger> taskTimesPer15Seconds = new HashMap<>();
    public static Map<String, AtomicInteger> errorTimesPer15Seconds = new HashMap<>();
    public static Map<String, AtomicInteger> tagTimesPer15Seconds = new HashMap<>();
    public static ArrayBlockingQueue<ReportInfo> arrayBlockingQueue = new ArrayBlockingQueue<>(10000);
    public static AtomicInteger topActiveCount = new AtomicInteger(0);
    public static AtomicInteger topQueueSize = new AtomicInteger(0);
    public static Map<String, ExecutorPeekRecordHolder> peekRecordHolderMap = new HashMap<>(0);

    public static void initTaskTimesPerMinuter() {
        for (String poolName : executorMap.keySet()) {
            MonitorCache.taskTimesPer15Seconds.put(poolName, new AtomicInteger(0));
            MonitorCache.errorTimesPer15Seconds.put(poolName, new AtomicInteger(0));
            MonitorCache.tagTimesPer15Seconds.put(poolName, new AtomicInteger(0));
        }
    }
}
