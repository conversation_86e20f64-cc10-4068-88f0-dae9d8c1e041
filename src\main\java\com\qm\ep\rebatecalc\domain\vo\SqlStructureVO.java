package com.qm.ep.rebatecalc.domain.vo;

import com.qm.ep.rebatecalc.enumerate.CalcObjectTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Schema(description = "sql结构信息表")
@Data
public class SqlStructureVO {

    @Schema(description = "序列化Id")
    private static final long serialVersionUID = 1L;

    @Schema(description = "不同商务政策下的计算方案临时表集合")
    private List<Map<CalcObjectTypeEnum, Map<String, String>>> planTemporaryTableSqlList;

    @Schema(description = "temporaryTableSql")
    private Map<CalcObjectTypeEnum, Map<String, String>> temporaryTableSql;

    @Schema(description = "temporaryTableSqlFormatted")
    private String temporaryTableSqlFormatted;

    @Schema(description = "sql")
    private String sql;

    @Schema(description = "sqlFormatted")
    private String sqlFormatted;

    @Schema(description = "字段列表")
    private List<String> fields;

}