<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qm.tds</groupId>
        <artifactId>tds-base-service-parent</artifactId>
        <version>7.0.0-rebate-SNAPSHOT</version>
    </parent>
    <artifactId>tds-service-rebate-calc</artifactId>
    <version>0.0.4-SNAPSHOT</version>
    <name>tds-service-rebate-calc</name>
    <description>返利试算服务</description>
    <dependencies>
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-dynamic-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-lock</artifactId>
        </dependency>
        <!-- junit test case -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--  ep junit test case -->
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>ep-test-base-common</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.qm.tds</groupId>
            <artifactId>tds-base-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-jpa</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>nl.jqno.equalsverifier</groupId>
            <artifactId>equalsverifier</artifactId>
            <version>3.5</version>
            <scope>test</scope>
        </dependency>
        <!--maven中引入-->
        <!--<dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.0</version>
        </dependency>

        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            &lt;!&ndash; go to https://search.maven.org/search?q=tencentcloud-sdk-java and get the latest version. &ndash;&gt;
            &lt;!&ndash; 请到https://search.maven.org/search?q=tencentcloud-sdk-java查询所有版本，最新版本如下 &ndash;&gt;
            <version>3.1.224</version>
        </dependency>-->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>10.1.34</version>
        </dependency>

        <!-- kingbase -->
        <dependency>
            <groupId>cn.com.kingbase</groupId>
            <artifactId>kingbase8</artifactId>
            <version>8.6.1</version>
        </dependency>

    </dependencies>

    <build>
        <!-- 坐标+版本号 -->
        <finalName>${project.artifactId}-${project.version}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <!-- Maven仓库地址 -->
    <repositories>
        <repository>
            <id>qm_maven_center</id>
            <name>qm dms maven center</name>
            <url>https://jfapp.qm.cn:8003/repository/qm-dms-public/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <properties>
        <flowin>com.qm.tds.base.controller</flowin>
    </properties>

    <!-- 插件Maven仓库地址 -->
    <pluginRepositories>
        <pluginRepository>
            <id>qm_maven_center</id>
            <name>qm dms maven center</name>
            <url>https://jfapp.qm.cn:8003/repository/qm-dms-public/</url>
        </pluginRepository>
    </pluginRepositories>

</project>
