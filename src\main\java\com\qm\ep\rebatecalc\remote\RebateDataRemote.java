package com.qm.ep.rebatecalc.remote;

import com.qm.ep.rebatecalc.config.AsyncFeignConfig;
import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcDTO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@FeignClient(contextId = "tds-service-rebate-data",name = "tds-service-rebate-data",configuration = AsyncFeignConfig.class)
public interface RebateDataRemote {

    /**
     * 入账
     * @param execCalcDTO 参数
     * @return 返回
     */
    @PostMapping(value = "/EpData/applyEntryAccount", produces = "application/json")
    JsonResultVo<Object> applyEntryAccount(@RequestBody ExecFormalCalcHistoryDO execCalcDTO);



}
