package com.qm.ep.rebatecalc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcResultDO;
import com.qm.ep.rebatecalc.domain.bean.ExecTrialCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.bean.ExecTrialCalcResultDO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcResultDTO;
import com.qm.ep.rebatecalc.domain.vo.SqlStructureVO;
import com.qm.ep.rebatecalc.mapper.ExecFormalCalcHistoryMapper;
import com.qm.ep.rebatecalc.mapper.ExecFormalCalcResultMapper;
import com.qm.ep.rebatecalc.remote.RebateBaseRemote;
import com.qm.ep.rebatecalc.service.ExecFormalCalcResultService;
import com.qm.ep.rebatecalc.service.SystemConfigService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExecFormalCalcResultServiceImpl extends QmBaseServiceImpl<ExecFormalCalcResultMapper, ExecFormalCalcResultDO> implements ExecFormalCalcResultService {


    @Resource
    private SystemConfigService systemConfigService;
    @Resource
    private ExecFormalCalcHistoryMapper execFormalCalcHistoryMapper;
    @Resource
    private ExecFormalCalcResultMapper execFormalCalcResultMapper;
    @Resource
    private RebateBaseRemote rebateBaseRemote;

    @Override
    public QmPage<ExecFormalCalcResultDO> queryCalcResult(ExecCalcResultDTO execCalcResultDTO) {
        // 加日志，提示程序执行时间
        log.info("查询计算结果开始:" + new Date());
        LoginKeyDO userInfo = BootAppUtil.getLoginKey();
        QmQueryWrapper<ExecFormalCalcResultDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcResultDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(ExecFormalCalcResultDO::getHistoryId, execCalcResultDTO.getHistoryId());

        ExecFormalCalcHistoryDO execCalcHistoryDO = execFormalCalcHistoryMapper.selectById(execCalcResultDTO.getHistoryId());
        String company = systemConfigService.getValueByCode("company");
        if("bx".equals(company)){
            JsonResultVo<Boolean> isBxAdminVO = rebateBaseRemote.checkPersonAtTopLevel(userInfo.getOperatorId());
            if(isBxAdminVO.getCode() != 200){
                log.error(isBxAdminVO.getMsg());
                throw new QmException("系统发生错误，请联系管理员");
            }
            boolean isBxAdmin = isBxAdminVO.getData();
            if(!isBxAdmin){
                // 如果不是管理员
                if(execCalcHistoryDO != null) {
                    SqlStructureVO sqlStructureVO = JSONUtils.packingDOFromJsonStr(execCalcHistoryDO.getSqlStructure(), SqlStructureVO.class);
                    List<String> fields = sqlStructureVO.getFields();
                    List<String> sumFields = new ArrayList<>();
                    List<String> fsFile = Lists.newArrayList();
                    // 查询是否包含经销商代码字段，
                    String dealerCodeField = null;
                    for(int i = 1; i <= fields.size(); i++){
                        if("经销商代码".equals(fields.get(i-1))){
                            dealerCodeField = "field" + i;
                        }
                        if("金额".equals(fields.get(i-1))){
                            fsFile.add("field" + i);
                        }
                        sumFields.add("field" + i);
                    }
                    // 如果存在经销商代码字段，则需要通过经销商代码进行过滤
                    if(CharSequenceUtil.isNotEmpty(dealerCodeField)){
                        JsonResultVo<List<String>> leafOrgVO = rebateBaseRemote.getLeafOrg(userInfo.getOperatorId());
                        if(isBxAdminVO.getCode() != 200){
                            log.error(isBxAdminVO.getMsg());
                            throw new QmException("系统发生错误，请联系管理员");
                        }
                        List<String> dealerCodeList = leafOrgVO.getData();
                        if(CollUtil.isNotEmpty(dealerCodeList)){
                            queryWrapper.in(dealerCodeField, dealerCodeList);
                            QmPage<ExecFormalCalcResultDO> page = table(queryWrapper, execCalcResultDTO);
                            // 把计算结果的状态传递给明细
                            List<ExecFormalCalcResultDO> resultList = page.getItems();
                            if(CollectionUtils.isNotEmpty(resultList)) {
                                resultList.forEach(result -> {
                                    result.setAccountEntryStatus(execCalcHistoryDO.getFinEntryState());
                                });
                            }

                            Map<String, Object> sumResult = execFormalCalcResultMapper.selectCalcResultSum(execCalcResultDTO.getHistoryId(), sumFields, dealerCodeField, dealerCodeList);
                            // 删除非金额的字段
                            sumResult.keySet().removeIf(key -> !fsFile.contains(key));
                            page.setExtension(sumResult);
                            return page;
                        }else{
                            QmPage<ExecFormalCalcResultDO> page = new QmPage<>();
                            page.setItems(Collections.emptyList());
                            page.setExtension(Collections.emptyMap());
                            return page;
                        }
                    }
                }
            }
        }
        QmPage<ExecFormalCalcResultDO> list = table(queryWrapper, execCalcResultDTO);
        if(execCalcHistoryDO != null){
            String sumResult = execCalcHistoryDO.getSumResult();
            if(BootAppUtil.isnotNullOrEmpty(sumResult)) {
                SqlStructureVO sqlStructureVO = JSONUtils.packingDOFromJsonStr(execCalcHistoryDO.getSqlStructure(), SqlStructureVO.class);
                List<String> fields = sqlStructureVO.getFields();
                List<String> fsFile = Lists.newArrayList();
                for(int i = 1; i <= fields.size(); i++){
                    if((fields.get(i-1)).contains("金额")){
                        fsFile.add("field" + i);
                    }
                }

                Map<String, Object> extMap = JSONUtils.jsonToMap(sumResult);
                // 删除非金额的字段
                extMap.keySet().removeIf(key -> !fsFile.contains(key));
                list.setExtension(extMap);
            }
        }
        if(CollectionUtils.isNotEmpty(list.getItems())) {
            list.getItems().forEach(rs -> {
                rs.setAccountEntryStatus(execCalcHistoryDO.getFinEntryState());
                rs.setResultId(String.valueOf(rs.getId()));
            });
        }
        log.info("查询计算结果结束:" + new Date());
        return list;
    }

    /**
     * 通过SQL 保存
     *
     * @param map 参数
     * @return 返回
     */
    @Override
    public int saveBySql(Map<String, Object> map) {
        return baseMapper.insertBySql(map);
    }

    /**
     * 通过SQL执行
     *
     * @param map 参数
     * @return 返回
     */
    @Override
    public List<Map> getBySql(Map<String, Object> map) {
        return baseMapper.selectBySql(map);
    }

    @Override
    public List<Integer> getAllId(ExecFormalCalcResultDO tempDTO) {
        return execFormalCalcResultMapper.getAllId(tempDTO.getHistoryId());
    }


}
