package com.qm.ep.rebatecalc.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.rebatecalc.domain.bean.ReportDataDO;
import com.qm.ep.rebatecalc.domain.dto.ReportDataDTO;
import com.qm.ep.rebatecalc.mapper.ReportDataMapper;
import com.qm.ep.rebatecalc.service.ReportDataService;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.dynamic.constant.DataSourceType;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.TableUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReportDataServiceImpl extends QmBaseServiceImpl<ReportDataMapper, ReportDataDO> implements ReportDataService {

    @Autowired
    private ReportDataMapper reportDataMapper;

    @Override
    @DS(DataSourceType.W)
    public QmPage<ReportDataDO> queryTable(String tenantName, ReportDataDTO paramDTO) {
        if(BootAppUtil.isnotNullOrEmpty(paramDTO.getReportId())){
            QmQueryWrapper<ReportDataDO> queryWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<ReportDataDO> wrapper = queryWrapper.lambda();
            wrapper.eq(ReportDataDO::getReportId, paramDTO.getReportId());
            TableUtils.appendTableAdditional(queryWrapper, paramDTO, ReportDataDO.class);
            IPage<ReportDataDO> queryPage = TableUtils.convertToIPage(paramDTO);
            IPage<ReportDataDO> page = reportDataMapper.selectPage(queryPage, queryWrapper);
            return TableUtils.convertQmPageFromMpPage(page);
        }
        throw new QmException("当前报表不存在");
    }

    @Override
    @DS(DataSourceType.W)
    public void deleteData(String tenantName, String reportId) {
        Map<String, Object> deleteMap = new HashMap<>(1);
        deleteMap.put("REPORT_ID", reportId);
        reportDataMapper.deleteByMap(deleteMap);
    }

}
