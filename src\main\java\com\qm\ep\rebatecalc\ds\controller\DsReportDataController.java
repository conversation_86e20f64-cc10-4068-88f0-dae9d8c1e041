package com.qm.ep.rebatecalc.ds.controller;

import cn.hutool.core.util.ReflectUtil;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.qm.ep.rebatecalc.domain.bean.ReportDataDO;
import com.qm.ep.rebatecalc.domain.dto.ReportDataDTO;
import com.qm.ep.rebatecalc.service.ReportDataService;
import com.qm.ep.rebatecalc.service.TenantDealerService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.dynamic.constant.DataSourceType;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/ds/reportData")
@Tag(name = "报表数据", description = "报表数据")
@Slf4j
public class DsReportDataController extends BaseController {

    @Autowired
    private ReportDataService reportDataService;
    @Autowired
    private TenantDealerService tenantDealerService;
    @Autowired
    private DataSource dataSource;

    @Operation(summary = "查询报表数据", description = "查询报表数据")
    @PostMapping("/getDataList")
    public JsonResultVo<QmPage<ReportDataDO>> getDataList(@RequestBody ReportDataDTO paramDTO){
        JsonResultVo<QmPage<ReportDataDO>> result = new JsonResultVo<>();
        String tenantName = tenantDealerService.getDsName(paramDTO.getDealerCode());
        QmPage<ReportDataDO> list = reportDataService.queryTable(tenantName, paramDTO);
        result.setData(list);
        return result;
    }

    @Operation(summary = "删除报表数据", description = "删除报表数据")
    @PostMapping("/deleteData")
    public void deleteData(@RequestBody String reportId){
        LoginKeyDO userInfo = getUserInfo();
        String tenantId = userInfo.getTenantId();
        DynamicRoutingDataSource ds = (DynamicRoutingDataSource) ReflectUtil.getFieldValue(dataSource, "dataSource");
        List<String> dsNames = ds.getDataSources().keySet().stream()
                .filter(i -> i.startsWith(tenantId + "dealer"))
                .map(i -> i.replace(tenantId, ""))
                .collect(Collectors.toList());
        dsNames.add(DataSourceType.W);
        dsNames.forEach(dsName -> reportDataService.deleteData(dsName, reportId));
    }

}
