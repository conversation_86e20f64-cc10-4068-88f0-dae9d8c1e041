package com.qm.ep.rebatecalc.controller;

import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcResultDO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcResultDTO;
import com.qm.ep.testapi.constant.UserConstants;
import com.qm.ep.testapi.controller.BaseTestController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import lombok.extern.slf4j.Slf4j;
import nl.jqno.equalsverifier.EqualsVerifier;
import nl.jqno.equalsverifier.Warning;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class ExecFormalCalcResultControllerTest extends BaseTestController<ExecFormalCalcResultController> {

    @Before
    public void beforeMethod() {
        this.initUser(UserConstants.USER_CODE_COMPANY);
    }

    /**
     * 覆盖dto
     */
    @Test
    public void moduleDtoTest() {
        EqualsVerifier.simple().forClass(ExecCalcResultDTO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
    }

    /**
     * 覆盖do
     */
    @Test
    public void moduleDoTest() {
        EqualsVerifier.simple().forClass(ExecFormalCalcResultDO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
    }

    @Test
    public void table() {
        ExecCalcResultDTO dto = this.moduleEmpty(ExecCalcResultDTO.class);
        JsonResultVo<QmPage<ExecFormalCalcResultDO>> resultVo;

        resultVo = this.testController.table(dto);
        this.assertJsonResultVo(resultVo);
        Assert.assertEquals("查询到记录！", 0, resultVo.getData().getTotal());

        dto = this.moduleFill(ExecCalcResultDTO.class);
        resultVo = this.testController.table(dto);
        this.assertJsonResultVo(resultVo);
        Assert.assertEquals("查询到记录！", 0, resultVo.getData().getTotal());
    }

}