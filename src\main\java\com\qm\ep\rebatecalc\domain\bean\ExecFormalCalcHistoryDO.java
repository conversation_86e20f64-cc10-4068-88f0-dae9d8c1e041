package com.qm.ep.rebatecalc.domain.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.ep.rebatecalc.enumerate.AccountEntryStateEnum;
import com.qm.ep.rebatecalc.enumerate.CalcObjectTypeEnum;
import com.qm.ep.rebatecalc.enumerate.ExecTypeEnum;
import com.qm.ep.rebatecalc.enumerate.StateEnum;
import com.qm.ep.rebatecalc.serializer.CommonEnumCodeDeserializer;
import com.qm.ep.rebatecalc.serializer.CommonEnumCodeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("exec_formal_calc_history")
@Schema(description = "计算历史主表对象")
public class ExecFormalCalcHistoryDO implements Serializable {

    @Schema(description = "序列化id")
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "政策ID")
    @TableField("policyId")
    private String policyId;

    @Schema(description = "计算对象ID")
    @TableField("objectId")
    private String objectId;

    @Schema(description = "计算对象类型")
    @TableField("objectType")
    private CalcObjectTypeEnum objectType;

    @Schema(description = "计算类型")
    @TableField("execType")
    private ExecTypeEnum execType;

    @Schema(description = "历史版本号")
    @TableField("calcVersion")
    private Integer calcVersion;

    @Schema(description = "状态")
    @TableField("state")
    private StateEnum state;

    @Schema(description = "申请入帐状态")
    @TableField("applyEntryState")
    @JSONField(serializeUsing = CommonEnumCodeSerializer.class,
            deserializeUsing = CommonEnumCodeDeserializer.class)
    private AccountEntryStateEnum applyEntryState;

    @Schema(description = "入账状态")
    @TableField(exist = false)
    private String passEntryState;

    @Schema(description = "消息MQ状态")
    @TableField("mqState")
    private StateEnum mqState;

    @Schema(description = "试算开始时间")
    @TableField(value = "begin")
    private Date begin;

    @Schema(description = "试算结束时间")
    @TableField(value = "end")
    private Date end;

    @Schema(description = "语句sql")
    @TableField("sqlStructure")
    private String sqlStructure;

    @Schema(description = "日志log")
    @TableField("log")
    private String log;

    @Schema(description = "结果求和")
    @TableField("sumResult")
    private String sumResult;

    @Schema(description = "执行线程名称")
    @TableField("executorThreadName")
    private String executorThreadName;

    @Schema(description = "消息mq log")
    @TableField("mqLog")
    private String mqLog;

    @Schema(description = "创建者")
    @TableField(value = "createby", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建日期")
    @TableField(value = "createon", fill = FieldFill.INSERT)
    private Date createOn;

    @Schema(description = "更新者")
    @TableField(value = "updateby", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @Schema(description = "更新日期")
    @TableField(value = "updateon", fill = FieldFill.INSERT_UPDATE)
    private Date updateOn;

    @Schema(description = "创建人名称")
    @TableField(exist = false)
    private String createByName;

    @Schema(description = "更新人名称")
    @TableField(exist = false)
    private String updateByName;

    @Schema(description = "逻辑删除is_deleted")
    @TableField("isDeleted")
    private String isDeleted;

    @Schema(description = "版本record_version")
    @TableField("recordVersion")
    private String recordVersion;

    @Schema(description = "是否自动转底表")
    @TableField("autoTurnTable")
    private String autoTurnTable;

    @Schema(description = "底表名称")
    @TableField("tableName")
    private String tableName;

    @Schema(description = "额外信息")
    @TableField("extra")
    private String extra;

    @Schema(description = "是否自动入账")
    @TableField("autoEnterAccount")
    private String autoEnterAccount;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Date dtstamp;

    @Schema(description = "财务审核状态finEntryState")
    @TableField("finEntryState")
    private String finEntryState;

    @Schema(description = "发送失败原因")
    @TableField(value = "applyEntryContent")
    private String applyEntryContent;
}
