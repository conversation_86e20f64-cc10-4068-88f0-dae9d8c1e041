package com.qm.ep.rebatecalc.service.impl;

import com.qm.ep.rebatecalc.domain.bean.EntryAccountDataDO;
import com.qm.ep.rebatecalc.mapper.EntryAccountDataMapper;
import com.qm.ep.rebatecalc.service.EntryAccountDataService;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Service
public class EntryAccountDataServiceImpl extends QmBaseServiceImpl<EntryAccountDataMapper, EntryAccountDataDO> implements EntryAccountDataService {

}

