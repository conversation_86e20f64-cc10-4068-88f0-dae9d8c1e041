package com.qm.ep.rebatecalc.service;

import com.qm.ep.rebatecalc.domain.dto.ExecCalcDTO;
import org.springframework.scheduling.annotation.Async;

/**
 * <AUTHOR>
 */
public interface ExecTrialCalcService {

    /**
     * 试算准备
     * @param execCalcDTO 参数
     * @return 返回
     */
    String prepareCalc(ExecCalcDTO execCalcDTO);

    /**
     * 发起计算对象试算
     * @param historyId 试算历史ID
     * @throws InterruptedException 异常
     */
    @Async("execCalcAsync")
    void execCalc(String historyId,String lockKey) throws InterruptedException;
}
