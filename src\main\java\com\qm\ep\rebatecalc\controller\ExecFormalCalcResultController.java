package com.qm.ep.rebatecalc.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcResultDO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcResultDTO;
import com.qm.ep.rebatecalc.service.ExecFormalCalcHistoryService;
import com.qm.ep.rebatecalc.service.ExecFormalCalcResultService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/execFormalCalcResult")
@Tag(description = "[author:50013723]", name = "计算对象计算结果")
public class ExecFormalCalcResultController extends BaseController {

    @Resource
    private ExecFormalCalcHistoryService execCalcHistoryService;

    @Resource
    private ExecFormalCalcResultService execCalcResultService;

    @Operation(summary = "查询计算对象计算结果列表", description= "[author:50013723]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ExecFormalCalcResultDO>> table(@RequestBody ExecCalcResultDTO execCalcResultDTO) {
        JsonResultVo<QmPage<ExecFormalCalcResultDO>> ret = new JsonResultVo<>();
        QmPage<ExecFormalCalcResultDO> list = execCalcResultService.queryCalcResult(execCalcResultDTO);
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "通过政策id，删除计算因子计算结果记录", description= "[author:50013723]")
    @PostMapping("/deleteByResultPolicyId")
    public boolean deleteByResultPolicyId(@RequestBody String policyId) {
        QmQueryWrapper<ExecFormalCalcHistoryDO> historyWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcHistoryDO> historyLambdaWrapper = historyWrapper.lambda();
        historyLambdaWrapper.eq(ExecFormalCalcHistoryDO::getPolicyId, policyId);
        List<ExecFormalCalcHistoryDO> historyList = execCalcHistoryService.list(historyLambdaWrapper);
        List<String> historyIds = historyList.stream().map(ExecFormalCalcHistoryDO::getId).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(historyIds)){
            QmQueryWrapper<ExecFormalCalcResultDO> resultWrapper = new QmQueryWrapper<>();
            LambdaQueryWrapper<ExecFormalCalcResultDO> resultlambdaWrapper = resultWrapper.lambda();
            resultlambdaWrapper.eq(ExecFormalCalcResultDO::getHistoryId, historyIds);
            return execCalcResultService.remove(resultlambdaWrapper);
        }
        return false;
    }

    @Operation(summary = "获取计算对象计算结果列表", description= "[author:50013723]")
    @PostMapping("/getExecCalcResultList")
    public JsonResultVo<List<ExecFormalCalcResultDO>> getExecCalcResultList(@RequestBody ExecCalcResultDTO execCalcResultDTO) {
        JsonResultVo<List<ExecFormalCalcResultDO>> ret = new JsonResultVo<>();

        QmQueryWrapper<ExecFormalCalcResultDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcResultDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(ExecFormalCalcResultDO::getHistoryId, execCalcResultDTO.getHistoryId());

        List<ExecFormalCalcResultDO> list = execCalcResultService.list(queryWrapper);
        ret.setData(list);
        return ret;
    }

    @Operation(summary = "计算结果页全选", description= "[author:50013723]")
    @PostMapping("/getAllList")
    public JsonResultVo<List<Integer>> getListAll(@RequestBody ExecFormalCalcResultDO tempDTO) {
        List<Integer> ids = execCalcResultService.getAllId(tempDTO);
        JsonResultVo<List<Integer>> ret = new JsonResultVo<>();
        ret.setData(ids);
        return ret;
    }

}
