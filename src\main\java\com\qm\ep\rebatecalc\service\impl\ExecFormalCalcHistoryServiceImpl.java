package com.qm.ep.rebatecalc.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatecalc.domain.bean.EntryAccountDataDO;
import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.bean.ExecFormalCalcResultDO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcHistoryDTO;
import com.qm.ep.rebatecalc.domain.vo.SqlStructureVO;
import com.qm.ep.rebatecalc.mapper.ExecFormalCalcHistoryMapper;
import com.qm.ep.rebatecalc.remote.RebateBaseRemote;
import com.qm.ep.rebatecalc.remote.RebateDataRemote;
import com.qm.ep.rebatecalc.service.EntryAccountDataService;
import com.qm.ep.rebatecalc.service.ExecFormalCalcHistoryService;
import com.qm.ep.rebatecalc.service.ExecFormalCalcResultService;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.JSONUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExecFormalCalcHistoryServiceImpl extends QmBaseServiceImpl<ExecFormalCalcHistoryMapper, ExecFormalCalcHistoryDO> implements ExecFormalCalcHistoryService {

    public static final String ONE = "1";
    public static final String FT = "52";
    @Resource
    RebateDataRemote rebateDataRemote;

    @Resource
    RebateBaseRemote rebateBaseRemote;

    @Resource
    private EntryAccountDataService entryAccountDataService;

    @Resource
    private ExecFormalCalcResultService execCalcResultService;

    @Override
    public void autoApplyEntryAccount(ExecFormalCalcHistoryDO execCalcHistoryDO) {
        rebateDataRemote.applyEntryAccount(execCalcHistoryDO);
    }

    @Override
    public void autoApply(ExecFormalCalcHistoryDO execCalcHistoryDO) {
        rebateBaseRemote.autoApply(execCalcHistoryDO);
    }

    @Override
    public QmPage<ExecFormalCalcHistoryDO> tableList(ExecCalcHistoryDTO execCalcHistoryDTO) {
        QmQueryWrapper<ExecFormalCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(BootAppUtil.isnotNullOrEmpty(execCalcHistoryDTO.getPolicyId()), ExecFormalCalcHistoryDO::getPolicyId, execCalcHistoryDTO.getPolicyId())
                .eq(ExecFormalCalcHistoryDO::getObjectId, execCalcHistoryDTO.getObjectId())
                .eq(ExecFormalCalcHistoryDO::getObjectType, execCalcHistoryDTO.getObjectType());
        if(BootAppUtil.isnotNullOrEmpty(execCalcHistoryDTO.getVflag()) && ONE.equals(execCalcHistoryDTO.getVflag())){
            lambdaWrapper.in(ExecFormalCalcHistoryDO::getFinEntryState, "50","51","52","53","54","55","56");
        }
        QmPage<ExecFormalCalcHistoryDO> page = this.table(queryWrapper, execCalcHistoryDTO);
        List<ExecFormalCalcHistoryDO> list = page.getItems();
        list.forEach(item -> {
            String historyId = item.getId();
            LambdaQueryWrapper<ExecFormalCalcResultDO> resultWrapper = new QmQueryWrapper<ExecFormalCalcResultDO>().lambda();
            resultWrapper.eq(ExecFormalCalcResultDO::getHistoryId, historyId);

            LambdaQueryWrapper<EntryAccountDataDO> statusWrapper = new QmQueryWrapper<EntryAccountDataDO>().lambda();
            statusWrapper.eq(EntryAccountDataDO::getUniqueKey, historyId);
            List<EntryAccountDataDO> entryAccountDataDOList = entryAccountDataService.list(statusWrapper);
            long passCount=0;
            long rejectCount=0;
            long unAuditCount=0;
            if(!entryAccountDataDOList.isEmpty()){
                passCount = entryAccountDataDOList.stream().filter(f->"53".equals(f.getStateCode())).count();
                rejectCount =entryAccountDataDOList.stream().filter(f->"55".equals(f.getStateCode())).count();
                unAuditCount =entryAccountDataDOList.stream().filter(f->"52".equals(f.getStateCode())).count();
            }

            if(FT.equals(item.getFinEntryState())
                    || "53".equals(item.getFinEntryState())
                    || "54".equals(item.getFinEntryState())
                    || "55".equals(item.getFinEntryState())
                    || "56".equals(item.getFinEntryState())) {
                String sb = StrUtil.format("审核通过({})", passCount) +
                        StrUtil.format("驳回({})", rejectCount) +
                        StrUtil.format("未审核({})", unAuditCount);
                item.setPassEntryState(CharSequenceUtil.format(sb));
            }

            if("auto".equals(item.getExecType().getCode())) {
                item.setCreateByName("system");
                item.setUpdateByName("system");
            }
        });
        return page;
    }

    @Override
    public List<String> getFields(ExecCalcHistoryDTO execCalcHistoryDTO) {
        QmQueryWrapper<ExecFormalCalcHistoryDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcHistoryDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.eq(ExecFormalCalcHistoryDO::getId, execCalcHistoryDTO.getId());

        ExecFormalCalcHistoryDO execCalcHistoryDO = this.getOne(lambdaWrapper);
        if(execCalcHistoryDO==null) {
            throw new QmException("未找到计算历史记录！");
        }

        SqlStructureVO sqlStructureVO = JSONUtils.packingDOFromJsonStr(execCalcHistoryDO.getSqlStructure(), SqlStructureVO.class);
        return sqlStructureVO.getFields();

    }

    @Override
    public void deleteList(List<String> deleteIds) {
        if(deleteIds.isEmpty()) {
            throw new QmException("未选择删除记录！");
        }
        this.removeByIds(deleteIds);

        QmQueryWrapper<ExecFormalCalcResultDO> queryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecFormalCalcResultDO> lambdaWrapper = queryWrapper.lambda();
        lambdaWrapper.in(ExecFormalCalcResultDO::getHistoryId, deleteIds);
        execCalcResultService.remove(lambdaWrapper);
    }
}
