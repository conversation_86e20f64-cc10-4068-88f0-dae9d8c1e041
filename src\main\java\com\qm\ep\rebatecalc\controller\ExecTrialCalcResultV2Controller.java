package com.qm.ep.rebatecalc.controller;

import com.qm.ep.rebatecalc.domain.bean.ExecTrialCalcResultDO;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcResultDTO;
import com.qm.ep.rebatecalc.service.ExecTrialCalcResultService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/execTrialCalcResultV2")
@Tag(name = "计算对象试算结果", description = "[author:50013723]")
public class ExecTrialCalcResultV2Controller extends BaseController {

    @Resource
    private ExecTrialCalcResultService execCalcResultService;

    @Operation(summary = "查询计算对象试算结果列表", description= "[author:50013723]")
    @PostMapping("/table")
    public JsonResultVo<QmPage<ExecTrialCalcResultDO>> table(@RequestBody ExecCalcResultDTO execCalcResultDTO) {
        JsonResultVo<QmPage<ExecTrialCalcResultDO>> ret = new JsonResultVo<>();
        QmPage<ExecTrialCalcResultDO> page = execCalcResultService.queryCalcResult(execCalcResultDTO);
        ret.setData(page);
        return ret;
    }

    @Operation(summary = "通过政策id，删除计算因子试算结果记录", description= "[author:50013723]")
    @PostMapping("/deleteByResultPolicyId")
    public int deleteByResultPolicyId(@RequestBody String policyId) {
        return execCalcResultService.deleteExecCalcResult(policyId);
    }
}
