package com.qm.ep.rebatecalc.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "政策基础信息表")
@Data
public class PolicyDTO extends JsonParamDto {

    @Schema(description = "序列化Id")
    private static final long serialVersionUID = 1L;
    @Schema(description = "主键")
    private String id;
    @Schema(description = "政策代码")
    private String vpolicycode;
    @Schema(description = "政策名称")
    private String vpolicyname;
    @Schema(description = "政策描述")
    private String vpolicydesc;
    @Schema(description = "创建开始时间")
    private Date startTime;
    @Schema(description = "创建结束时间")
    private Date endTime;
    @Schema(description = "关联车型")
    private String vapplyprdt;
    @Schema(description = "关联经销商")
    private String vapplyorg;
    @Schema(description = "特殊车辆设定")
    private String vtscartype;
    @Schema(description = "完成状态")
    private List<String> vfinishstates;
    @Schema(description = "结算类型")
    private List<String> vsmttypes;
    @Schema(description = "日期类型")
    private String datetype;

    @Schema(description = "组织代码")
    private String orgCode;
    @Schema(description = "目标版本")
    private String aimVersion;


}