package com.qm.ep.rebatecalc.monitor.threadpool.config;

import com.qm.ep.rebatecalc.monitor.threadpool.processor.MonitorAnnotationBeanPostProcessor;
import com.qm.ep.rebatecalc.monitor.threadpool.processor.ThreadPoolMonitorProcessor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class ThreadPoolMonitorAutoConfigure {

    @Bean
    public ThreadPoolMonitorProcessor threadPoolMonitorProcessor() {
        return new ThreadPoolMonitorProcessor();
    }

    @Bean
    public MonitorAnnotationBeanPostProcessor monitorAnnotationBeanPostProcessor(ThreadPoolMonitorProcessor threadPoolMonitorProcessor) {
        MonitorAnnotationBeanPostProcessor processor =  new MonitorAnnotationBeanPostProcessor();
        processor.addProcessor(threadPoolMonitorProcessor);
        return processor;
    }

}
