package com.qm.ep.rebatecalc.monitor.threadpool.job;

import com.qm.ep.rebatecalc.monitor.threadpool.config.MonitorCache;
import com.qm.ep.rebatecalc.monitor.threadpool.report.Reporter;
import com.qm.ep.rebatecalc.monitor.threadpool.report.ThreadPoolDetailInfo;
import com.qm.tds.api.util.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 统计每分钟执行了多少任务
 *
 * <AUTHOR>
 */
@Slf4j
public class TaskTimesWatcher implements Runnable {

    private final Reporter reporter;

    public TaskTimesWatcher() {
        this.reporter = SpringContextHolder.getBean(Reporter.class);
    }

    @Override
    public void run() {
        while (true) {
            try {
                for (String poolName : MonitorCache.taskTimesPer15Seconds.keySet()) {

                    AtomicInteger taskCount = MonitorCache.taskTimesPer15Seconds.get(poolName);
                    AtomicInteger errorCount = MonitorCache.errorTimesPer15Seconds.get(poolName);
                    AtomicInteger tagCount = MonitorCache.tagTimesPer15Seconds.get(poolName);
                    ThreadPoolDetailInfo threadPoolDetailInfo = new ThreadPoolDetailInfo();
                    threadPoolDetailInfo.setApplicationName(MonitorCache.applicationName);
                    threadPoolDetailInfo.setPoolName(poolName);
                    threadPoolDetailInfo.setPort(MonitorCache.port);
                    threadPoolDetailInfo.setIp(MonitorCache.ip);
                    reporter.doReportTaskTimes(taskCount.get(), threadPoolDetailInfo);
                    reporter.doReportErrorTaskTimes(errorCount.get(), threadPoolDetailInfo);
                    reporter.doReportTagTimes(tagCount.get(), threadPoolDetailInfo);
//                    log.info("[TaskTimesWatcher] do report,poolName is {},taskCount is {},tagCount is {},errorCount is {}", poolName, taskCount.get(), tagCount.get(), errorCount.get());
                    //减去之前15秒内已经计算过的次数
                    MonitorCache.taskTimesPer15Seconds.get(poolName).addAndGet(-1 * taskCount.get());
                    MonitorCache.errorTimesPer15Seconds.get(poolName).addAndGet(-1 * errorCount.get());
                    MonitorCache.tagTimesPer15Seconds.get(poolName).addAndGet(-1 * tagCount.get());
                }
                TimeUnit.SECONDS.sleep(15);
            } catch (InterruptedException e) {
                log.error("TaskTimesWatcher 线程执行出错!", e);
            }
        }
    }
}
