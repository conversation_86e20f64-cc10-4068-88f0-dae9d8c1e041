package com.qm.ep.rebatecalc.service.impl;

import com.qm.ep.rebatecalc.domain.dto.ExecCalcLogStructureDTO;
import com.qm.ep.rebatecalc.service.ExecCalcLogService;
import com.qm.tds.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExecCalcLogServiceImpl implements ExecCalcLogService {

    /**
     * 追加日志内容
     *
     * @param originLog 原始日志
     * @param newLog    要追加的新日志
     * @return 返回
     */
    @Override
    public String append(String originLog, String newLog) {
        List<ExecCalcLogStructureDTO> execCalcLogStructures = JSONUtils.packingDOListFromJsonStr(originLog, ExecCalcLogStructureDTO.class);
        ExecCalcLogStructureDTO execCalcLogStructureDTO = JSONUtils.packingDOFromJsonStr(newLog, ExecCalcLogStructureDTO.class);
        execCalcLogStructures.add(execCalcLogStructureDTO);
        return JSONUtils.beanToJson(execCalcLogStructures);
    }

    /**
     * 追加日志内容
     *
     * @param originLog 原始日志
     * @param newLog    要追加的新日志
     * @return 返回
     */
    @Override
    public String append(String originLog, ExecCalcLogStructureDTO newLog) {
        List<ExecCalcLogStructureDTO> execCalcLogStructures = JSONUtils.packingDOListFromJsonStr(originLog, ExecCalcLogStructureDTO.class);
        execCalcLogStructures.add(newLog);
        return JSONUtils.beanToJson(execCalcLogStructures);
    }

    /**
     * 追加日志内容
     *
     * @param originLog 原始日志
     * @param newLogs   要追加的新日志
     * @return 返回
     */
    @Override
    public String append(String originLog, List<ExecCalcLogStructureDTO> newLogs) {
        List<ExecCalcLogStructureDTO> execCalcLogStructures = JSONUtils.packingDOListFromJsonStr(originLog, ExecCalcLogStructureDTO.class);
        execCalcLogStructures.addAll(newLogs);
        return JSONUtils.beanToJson(execCalcLogStructures);
    }
}
