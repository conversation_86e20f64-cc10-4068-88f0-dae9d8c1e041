package com.qm.ep.rebatecalc.service;

import com.qm.ep.rebatecalc.domain.bean.ExecTrialCalcHistoryDO;
import com.qm.tds.api.service.IQmBaseService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface ExecTrialCalcHistoryService extends IQmBaseService<ExecTrialCalcHistoryDO> {

    @Async("dataClearAsync")
    void removeTrailHistoryAndResultTransactional(List<String> historyIds);
}
