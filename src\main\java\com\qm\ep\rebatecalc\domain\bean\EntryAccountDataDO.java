package com.qm.ep.rebatecalc.domain.bean;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("entry_account_data")
@Schema(description = "")
public class EntryAccountDataDO implements Serializable {

    @Schema(description = "序列化id")
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "每次入账唯一主键（正式计算历史表ID）")
    @TableField("uniqueKey")
    private String uniqueKey;

    @Schema(description = "返利项目代码，取自销售模块维护返利项目程序")
    @TableField("classItem")
    private String classItem;

    @Schema(description = "入账方式代码，取自字典项：RBTPOLICYACCT")
    @TableField("auditType")
    private String auditType;

    @Schema(description = "每条数据唯一代码（正式计算历史表ID + 正式计算结果表ID）")
    @TableField("billNo")
    private String billNo;

    @Schema(description = "系列")
    @TableField("series")
    private String series;

    @Schema(description = "经销商代码")
    @TableField("dealerCode")
    private String dealerCode;

    @Schema(description = "经销商名称")
    @TableField("dealerName")
    private String dealerName;

    @Schema(description = "返利金额")
    @TableField("rebateAmount")
    private String rebateAmount;

    @Schema(description = "备注（商务政策名称 + 经销商备注（如有） + VIN码）")
    @TableField("remark")
    private String remark;

    @Schema(description = "操作员ID")
    @TableField("operatorId")
    private String operatorId;

    @Schema(description = "状态码")
    @TableField("stateCode")
    private String stateCode;

    @Schema(description = "政策主键")
    @TableField("policyId")
    private String policyId;

    @Schema(description = "政策编码")
    @TableField("policyCode")
    private String policyCode;

    @Schema(description = "政策名臣")
    @TableField("policyName")
    private String policyName;

    @Schema(description = "任务实例编码")
    @TableField("taskinsanceCode")
    private String taskinsanceCode;

    @Schema(description = "任务流实例编码")
    @TableField("taskFlowinstanceCode")
    private String taskFlowinstanceCode;

    @Schema(description = "审批流主键")
    @TableField("processInstanceId")
    private String processInstanceId;

    @Schema(description = "创建者")
    @TableField("CREATEBY")
    private String createby;

    @Schema(description = "创建日期")
    @TableField("CREATEON")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createon;

    @Schema(description = "更新者")
    @TableField("UPDATEBY")
    private String updateby;

    @Schema(description = "更新日期")
    @TableField("UPDATEON")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateon;

    @Schema(description = "提交人")
    @TableField("submitBy")
    private String submitBy;

    @Schema(description = "提交人代码")
    @TableField("submitCode")
    private String submitCode;

    @Schema(description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;


}
