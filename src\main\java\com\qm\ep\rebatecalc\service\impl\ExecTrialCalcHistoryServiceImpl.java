package com.qm.ep.rebatecalc.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.rebatecalc.domain.bean.ExecTrialCalcHistoryDO;
import com.qm.ep.rebatecalc.domain.bean.ExecTrialCalcResultDO;
import com.qm.ep.rebatecalc.mapper.ExecTrialCalcHistoryMapper;
import com.qm.ep.rebatecalc.service.ExecTrialCalcHistoryService;
import com.qm.ep.rebatecalc.service.ExecTrialCalcResultService;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.dynamic.constant.DataSourceType;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExecTrialCalcHistoryServiceImpl extends QmBaseServiceImpl<ExecTrialCalcHistoryMapper, ExecTrialCalcHistoryDO> implements ExecTrialCalcHistoryService {


    @Resource
    private ExecTrialCalcResultService execCalcResultService;

    @Transactional(propagation= Propagation.REQUIRES_NEW)
    @DS(DataSourceType.W)
    @Override
    public void removeTrailHistoryAndResultTransactional(List<String> historyIds) {
        QmQueryWrapper<ExecTrialCalcResultDO> queryResultWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<ExecTrialCalcResultDO> lambdaResultWrapper = queryResultWrapper.lambda();
        lambdaResultWrapper.in(ExecTrialCalcResultDO::getHistoryId, historyIds);
        execCalcResultService.remove(lambdaResultWrapper);
        // execCalcResultService.removeBatchByIds(ids);
        this.removeByIds(historyIds);
    }
}
