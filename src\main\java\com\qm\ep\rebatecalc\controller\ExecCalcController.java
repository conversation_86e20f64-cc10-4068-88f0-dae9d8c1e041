package com.qm.ep.rebatecalc.controller;

import cn.hutool.core.collection.CollUtil;
import com.qm.ep.rebatecalc.domain.dto.ExecCalcDTO;
import com.qm.ep.rebatecalc.monitor.threadpool.report.redis.RedisService;
import com.qm.ep.rebatecalc.service.ExecCalcService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/execCalc")
@Tag(name = "计算对象试算", description = "[author:50013723]")
@Slf4j
public class ExecCalcController extends BaseController {

    @Autowired
    private RedisService redisService;
    @Resource
    private ExecCalcService execCalcService;

    @Operation(summary = "发起计算对象计算", description = "[author:50013723]")
    @PostMapping("/start")
    public JsonResultVo<String> start(@RequestBody ExecCalcDTO execCalcDTO) {
        log.info("开始执行计算对象计算======================================：{}", execCalcDTO);
        return execCalcService.startCalc(execCalcDTO);
    }

    /**
     * 由于政策的阶梯，没有正式的计算发起点
     * 目前需求，由定时任务发起
     * 一次发起，将对所有符合条件的政策进行阶梯计算，并存入正式计算结果表中
     * @return 返回
     */
    @Operation(summary = "政策阶梯统一计算", description= "[author:50013723]")
    @PostMapping("/calcLadder")
    public JsonResultVo<String> calcLadder() {
        execCalcService.calcLadder();

        JsonResultVo<String> result = new JsonResultVo<>();
        result.setMsg("阶梯计算已发起！");
        return result;
    }

    @Operation(summary = "清空redis的key", description= "[author:50013723]")
    @PostMapping("/clearCalcRedis")
    public String clearCalcRedis(@RequestBody ExecCalcDTO execCalcDTO) {
        String lockKey = CollUtil.join(Arrays.asList(execCalcDTO.getPolicyId(), execCalcDTO.getObjectId(), execCalcDTO.getObjectType(), execCalcDTO.getCalcType()), ":");
        redisService.del(lockKey);
        return "执行完毕";
    }

}
